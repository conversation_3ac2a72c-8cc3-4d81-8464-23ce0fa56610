<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<h1>Safari fix</h1>
<script>
  // Safari now suddenly blocks iframe cookie access, so we need to call this during some user interaction
  function safariFix() {
    console.log("Safari Fix Run");
    console.log(navigator.userAgent.search("Safari"));
    if (navigator.userAgent.search("Safari") >= 0 && navigator.userAgent.search("Chrome") < 0) {
      document.requestStorageAccess().then(() => {
        // Now we have first-party storage access!
        console.log("Got cookie access for Safari workaround");
        // Let's access some items from the first-party cookie jar
        document.cookie = "foo=bar";              // drop a test cookie
      },  () => { console.log('access denied') }).catch((error) => {
        // error obtaining storage access.
        console.log("Could not get access for Safari workaround: " + error);
      });
    }
  }
</script>
<script>
  window.addEventListener('load', safariFix);
</script>
</body>
</html>