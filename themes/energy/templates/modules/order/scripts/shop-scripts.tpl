<script type="text/javascript">



  function updateBasket(response) {
    if (response['ok']) {
      cm.ajaxGetBasketCont('shop');



    } else {
      cm.appendError(false, response['error']);
    }
  }


  function updateSelectedAttractionsView($variantsContainer, ctr) {
    const $summary = $variantsContainer.find('.grouped-variants-summary');
    const $confirmButton = $variantsContainer.find('.confirm-variants-btn');
    const $selectionMessage = $confirmButton.siblings('.attraction-selection-message');
    const basePromoValue = parseFloat($variantsContainer.closest('.shop-item-cont')
        .find('.basic-discount-text').text().match(/\d+/)) || 0;

    const currentSelection = selectedAttractions[ctr] || [];

    // Aktualizuj stan przycisku i komunikatu
    if (currentSelection.length === 0) {
        $confirmButton.addClass('disabled').prop('disabled', true);
        $selectionMessage.show();
    } else {
        $confirmButton.removeClass('disabled').prop('disabled', false);
        $selectionMessage.hide();
    }

    // Obliczanie cen i rabatów
    // 1. Pobierz cenę podstawową vouchera
    const baseVoucherPrice = parseFloat($variantsContainer.closest('.shop-item-cont')
        .find('.shop-item-price .bold').text().replace(/[^\d.,]/g, '').replace(',', '.')) || 0;

    // 2. Oblicz sumę cen wszystkich atrakcji
    let attractionsTotal = 0;
    currentSelection.forEach(attraction => {
        const price = parseFloat(attraction.attractionPrice.replace(/[^\d.,]/g, '').replace(',', '.'));
        attractionsTotal += price;
    });

    // 3. Oblicz całkowitą sumę zamówienia przed rabatami
    const totalBeforeDiscount = baseVoucherPrice + attractionsTotal;

    // 4. Oblicz sumę wszystkich rabatów
    let totalDiscountPercentage = basePromoValue; // Zaczynamy od rabatu podstawowego

    // 5. Dodaj rabaty z atrakcji
    currentSelection.forEach(attraction => {
        if (attraction.promoValue) {
            totalDiscountPercentage += attraction.promoValue;
        }
    });

    // 6. Oblicz końcową wartość rabatu
    const totalDiscountAmount = (totalBeforeDiscount * totalDiscountPercentage) / 100;

    // 7. Oblicz cenę końcową
    const finalTotal = totalBeforeDiscount - totalDiscountAmount;

    // Aktualizuj podsumowanie
    if ($summary.length) {
        $summary.find('.base-price').text(totalBeforeDiscount.toFixed(2) + ' zł');
        $summary.find('.discount').html(
            `-${totalDiscountAmount.toFixed(2)} zł <span style="color: #5cb85c;">(${totalDiscountPercentage}%)</span>`
        );
        $summary.find('.final-price').text(finalTotal.toFixed(2) + ' zł');
        $summary.show();

    }
}

  async function updateBasketWithFreshData(callback) { // Make function async
    try {
      const basketData = await cm.ajaxGetUpdatedBasket(); // Use await


      basketFreshData = basketData.data.widget.orders ? basketData.data.widget.orders : {};

      var ordersCount = basketData.data.orders_count; // basket items counter 

      if (ordersCount > 0) {
        $('.basket-counter').show();
        $('.basket-counter').addClass('not-empty')
        var basketCounter = $('.basket-counter').text(ordersCount);


      } else {
        $('.basket-counter').hide();
      }


      if (typeof callback === 'function') {
        callback();
      }
    } catch (error) {
      console.error("Error fetching basket data:", error);
    }
  }




  const selectedAttractions = {};

  const initializeShopAnimations = () => {
    // Remove loading class to allow transitions
    document.documentElement.classList.remove('js-loading');

    const tl = gsap.timeline({
      defaults: { ease: "power2.out" },
      onStart: () => {
        // Make elements visible when animation starts
        gsap.set([
          '.navbar-wrapper2',
          '.nav-categories li',
          '.shop-item-cont',
          '#sidebarkoszyk',
          '.shop-payment-wrapper',
          '.products-title-seperator',
          '.material-basket-tabs',
          '.alert-container .alert',
          '.shop-meta-logo-container'
        ], { visibility: 'visible' });
      },
      onComplete: () => {
        document.body.classList.remove('preload');
        gsap.set(['.navbar-wrapper2', '.nav-categories li', '.shop-item-cont', '#sidebarkoszyk',
          '.shop-payment-wrapper', '.products-title-seperator', '.material-basket-tabs',
          '.alert-container .alert', '.shop-meta-logo-container'
        ], { clearProps: 'all' });
      }
    });

    // Create parallel animation groups
    tl.addLabel('start')
      // Header elements animate together
      .fromTo('.navbar-wrapper2', { y: -20, opacity: 0 }, { y: 0, opacity: 1, duration: 0.4 },
        'start'
      )
      .fromTo('.shop-meta-logo-container', { opacity: 0, scale: 0.95 }, { opacity: 1, scale: 1, duration: 0.4 },
        'start+=0.1'
      )

      // Navigation elements animate together
      .addLabel('navigation', '-=0.2')
      .fromTo('.nav-categories li', { opacity: 0, y: 10 }, { opacity: 1, y: 0, duration: 0.3, stagger: 0.03 },
        'navigation'
      )
      .fromTo('.material-basket-tabs', { opacity: 0, y: 10 }, { opacity: 1, y: 0, duration: 0.3 },
        'navigation'
      )

      // Main content elements animate together
      .addLabel('content', '-=0.2')
      .fromTo('.shop-item-cont', { opacity: 0, y: 15 }, {
          opacity: 1,
          y: 0,
          duration: 0.4,
          stagger: {
            amount: 0.3, // Total stagger time
            from: "start"
          }
        },
        'content'
      )
      .fromTo('.products-title-seperator', { opacity: 0, y: 10 }, { opacity: 1, y: 0, duration: 0.3 },
        'content'
      )

      // Sidebar elements animate together
      .addLabel('sidebar', 'content-=0.2')
      .fromTo('#sidebarkoszyk', { opacity: 0, x: 30 }, { opacity: 1, x: 0, duration: 0.4 },
        'sidebar'
      )
      .fromTo('.shop-payment-wrapper', { opacity: 0, y: 10 }, { opacity: 1, y: 0, duration: 0.3 },
        'sidebar'
      )

      // Alerts animate last but quickly
      .fromTo('.alert-container .alert', { opacity: 0, y: -20 }, {
          opacity: 1,
          y: 0,
          duration: 0.25,
          ease: "back.out(1.2)"
        },
        'content+=0.1'
      );

    return tl;
  };


  // Wait for all resources to load
  window.addEventListener('load', () => {
    var currentTpl = {{$current_tpl|json_encode}};

    if (currentTpl === 'shop') {
      const timeline = initializeShopAnimations();
    }
  });


  var cc = new Common();
  $(document).ready(function() {




    function findLatestMatchingItem(productType, id, partner_id, ticketName, variantId) {
      if (basketFreshData[productType] && basketFreshData[productType].length > 0) {
        // Filter matching tickets and sort by id_basket in descending order
        var matchingTickets = basketFreshData[productType]
          .filter(ticket => {
            if (productType == 'kupon') {

              return (ticket.main_product.nazwa == ticketName) &&
                (!variantId || ticket.main_product.kwota_brutto == variantId) && (id == ticket.main_product
                  .id_emisji);
            } else {
              return (ticket.main_product.identyfikator == id || ticket.main_product.partner_id == partner_id) &&
                ticket.main_product.nazwa === ticketName &&
                (!variantId || ticket.main_product.bsid == variantId);
            }
          }).sort((a, b) => b.id_basket - a.id_basket); // Sort in descending order of id_basket

        if (matchingTickets.length > 0) {
          return matchingTickets[0]; // Return the first (latest) ticket
        }
      }
      return null;
    }

    function checkProductLimit(ticketId, variantId, itemCounter) {
      if (typeof itemCounter === 'undefined' || itemCounter === null || !itemCounter) {
        return true; // No limit set, always allow
      }



      let currentQuantity = 0;
      var productTypes = ['karnet', 'voucher', 'produkty_wirtualne', 'kupon', 'ubezpieczenie', 'karta'];

      for (var productType of productTypes) {
        if (basketFreshData[productType]) {
          currentQuantity += basketFreshData[productType].filter(item =>
            item.main_product.identyfikator === ticketId
          ).length; // Count occurrences of ticketId
        }
      }

      return currentQuantity < itemCounter;
    }

    $(document).on('click', '.basket-del-order', async function() {
      var $button = $(this);
      $button.prop('disabled', true);

      var id_basket = $(this).data('id-basket');
      var productType = $(this).data('product-type');
      var ticketId = $(this).data('ticket-id');
      var partnerId = $(this).data('partner-id');
      var ticketName = $(this).data('ticket-name');
      var variantId = $(this).data('variant-id');



      if (productType === 'kupon') {
        partnerId = $(this).data('parentid');
        ticketId = $(this).data('id-emisji');
        ticketName = $(this).data('rodzaj-kuponu');
        variantId = $(this).data('kwota-brutto');


      }
      var latestTicket = findLatestMatchingItem(productType, ticketId, partnerId, ticketName, variantId);







      if (latestTicket) {
        try {
          await ajaxDelFromBasket(latestTicket.id_basket);
          await updateBasketWithFreshData(updateQuantityButtons);
        } catch (error) {
          console.error("Error removing from basket:", error);
        }
      } else if (id_basket) { // Check if id_basket exists and is not empty
        try {
          await ajaxDelFromBasket(id_basket); // Use id_basket for deletion
          await updateBasketWithFreshData(updateQuantityButtons);
        } catch (error) {
          console.error("Error removing from basket with id_basket:", error);
        }
      } else {
        console.error("No matching ticket found to remove");
      }

      setTimeout(function() {
        $button.prop('disabled', false);
      }, 300);
    });
    var currentTpl = {{$current_tpl|json_encode}};



    var basketFreshData = {};


    async function updateBasketWithFreshData(callback) { // Make function async
      try {
        const basketData = await cm.ajaxGetUpdatedBasket(); // Use await


        basketFreshData = basketData.data.widget.orders ? basketData.data.widget.orders : {};

        var ordersCount = basketData.data.orders_count; // basket items counter 

        if (ordersCount > 0) {
          $('.basket-counter').show();
          $('.basket-counter').addClass('not-empty')
          var basketCounter = $('.basket-counter').text(ordersCount);


        } else {
          $('.basket-counter').hide();
        }


        if (typeof callback === 'function') {
          callback();
        }
      } catch (error) {
        console.error("Error fetching basket data:", error);
      }
    }

    updateBasketWithFreshData(function() {


      updateQuantityButtons();
    });




    var fbqPurchase = {{$data.fbq_purchase|json_encode}};

    if (fbqPurchase !== null) {
      fbq('track', 'Purchase', { value: fbqPurchase['amt'], currency: fbqPurchase['cur'] });

      /* <![CDATA[ */
      var google_conversion_id = 944201063;
      var google_conversion_language = 'en';
      var google_conversion_format = '3';
      var google_conversion_color = 'ffffff';
      var google_conversion_label = 'n5rzCLCw-GAQ57qdwgM';
      var google_remarketing_only = false;
      /* ]]> */

      /* <![CDATA[ */
      var google_conversion_id = 942776559;
      var google_conversion_language = 'en';
      var google_conversion_format = '3';
      var google_conversion_color = 'ffffff';
      var google_conversion_label = 'aV5OCM6_8WAQ78HGwQM';
      var google_remarketing_only = false;
      /* ]]> */
    }


    setShopHeight();


    $('li.shop-category').click(function() {
      if (!$(this).hasClass('active')) {
        var product = $(this).data('product');

        // Deactivate the current active category
        $('li.shop-category.active').removeClass('active');
        $(this).addClass('active');

        if (product == 'all') {
          // Hide all containers first
          gsap.to('div.shop-products-container', {
            opacity: 0,
            scale: 0.95,
            duration: 0.3,
            onComplete: function() {
              $('div.shop-products-container').show();
              // Animate showing with smooth scaling and opacity
              gsap.to('div.shop-products-container', { opacity: 1, scale: 1, duration: 0.4 });
            }
          });
        } else {
          // Hide all containers
          gsap.to('div.shop-products-container', {
            opacity: 0,
            scale: 0.95,
            duration: 0.3,
            onComplete: function() {
              $('div.shop-products-container').hide();

              // Show the selected container with a smooth animation
              $('div#shop-' + product + '-container').show();
              gsap.fromTo('div#shop-' + product + '-container', { opacity: 0, scale: 0.95 }, {
                opacity: 1,
                scale: 1,
                duration: 0.4
              });
            }
          });
        }
      }
    });

    $('select.shop-ticket-variant').change(function() {
      changeTicketVariant($(this));
    });

    $('select.shop-ticket-variant').each(function() {
      changeTicketVariant($(this));
    });

    function changeTicketVariant($select) {
        let cheapestOption = null;
        if ($select.find('option').length > 1) {
            let lowestPrice = Infinity;
            
            $select.find('option').each(function() {
                let priceStr = $(this).data('price')
                    .replace(/\s/g, '')
                    .replace(/[^\d.,]/g, '');
                
                const parts = priceStr.split(/[.,]/);
                if (parts.length > 1) {
                    const wholeNumber = parts.slice(0, -1).join('');
                    priceStr = wholeNumber + '.' + parts[parts.length - 1];
                }
                
                const price = parseFloat(priceStr);
                
                if (!isNaN(price) && price < lowestPrice) {
                    lowestPrice = price;
                    cheapestOption = $(this);
                }
            });
            
            if (cheapestOption) {
                $select.val(cheapestOption.val());
            }
        }

        var $option = $select.find('option:selected');
        var ctr = $option.data('ctr');
        var price = $option.data('price');
        var bPrice = $option.data('b-price');
        var saving = $option.data('saving');
        var omnibus = $option.data('omnibus_price');

        // Sprawdzamy czy jest zniżka
        var hasDiscount = parseFloat(bPrice.replace(/[^\d.,]/g, '').replace(',', '.')) > 
                         parseFloat(price.replace(/[^\d.,]/g, '').replace(',', '.'));

        if (cheapestOption) {
            // Dla ceny "od"
            $('span#shop-ticket-price' + ctr).html(
                '{{App::_Lang('od','F_Zamówienie')}} ' + price
            );
        } else {
            // Dla zwykłej ceny
            $('span#shop-ticket-price' + ctr).html(price);
        }
        
        if (hasDiscount) {
            $('span#shop-ticket-saving' + ctr).empty().append(saving);
            $('span#shop-ticket-base-price' + ctr).empty().append(bPrice);
            $('span#shop-ticket-omnibus' + ctr).empty().append(omnibus);
        } else {
            $('span#shop-ticket-saving' + ctr).empty();
            $('span#shop-ticket-base-price' + ctr).empty();
            $('span#shop-ticket-omnibus' + ctr).empty();
        }
    }

    $('select.shop-coupon-value').each(function() {
      changeCouponValue($(this));
    });

    $('select.shop-coupon-value').change(function() {
      changeCouponValue($(this));
    });

    function changeCouponValue($select) {
      var $option = $select.find('option:selected');
      var ctr = $option.data('ctr');
      var kwota = $option.val();

      kwota = cm.CC(kwota);
      $('span#shop-coupon-price' + ctr).empty().append(kwota);
    }

    $('button.shop-add-to-basket').click(async function() {
      var ctr = $(this).data('ctr');
      // var amount = $.trim($('input#shop-item-amount' + ctr).val());
      var amount = '1';
      var variantId = $(this).data('variant-id');
      var ticketId = $(this).data('ticket-id');
      var itemCounter = $(this).data('item-counter');

      if (!amount.length) {
        cm.appendError(false, '{{App::_Lang('Musisz podać ilość produktów!','F_Zamówienie')}}');
        return false;
      }
      // $('input#shop-item-amount' + ctr).val(1); // Resets the input field to 1

      var data = {};
      data.p_type = $('input#shop-item-p-type' + ctr).val();

      // $(this).hide();

      const qunaitityBtns = $(this).closest('.shop-item-cont').find('.quantity-selector')

      if (!checkProductLimit(ticketId, variantId, itemCounter)) {
        cm.appendError(false, '{{App::_Lang('Przekroczono limit dla tego produktu!','F_Zamówienie')}}');
        return false;
      }


      // qunaitityBtns.show();
      switch (data.p_type) {
        case 'voucher':
        case 'karnet':
          data.parentid = $('input#shop-item-parentid' + ctr).val();
          data.identyfikator = $('input#shop-item-id' + ctr).val();
          // data.bsid = $('select#shop-item-variant' + ctr).val();
          // Apply variant ID if it exists
          data.bsid = variantId || $('select#shop-item-variant' + ctr).val();
          break;

        case 'karta':
          data.parentid = $('input#shop-item-parentid' + ctr).val();
          data.identyfikator = $('input#shop-item-id' + ctr).val();
          break;

        case 'ubezpieczenie':
          data.partnerid = $('input#shop-item-partner-id' + ctr).val();
          data.insurer = $('input#shop-item-insurer' + ctr).val();
          break;

        case 'kupon':
          data.parentid = $(this).data('parent-id');
          data.id_emisji = $(this).data('id-emisji');
          data.kwota = $(this).data('kwota-brutto');
          data.identyfikator = $('input#shop-item-identyfikator' + ctr).val();
          data.rodzaj_kuponu = $(this).data('rodzaj-kuponu');
          data.id_product = $(this).data('id-product');
          data.nazwa = $(this).data('ticket-name');

        case 'produkt_wirtualny':
          data.parentid = $('input#shop-item-parentid' + ctr).val();
          data.identyfikator = $('input#shop-item-id' + ctr).val();
          break;
      }

      data.amount = amount;

      var cart = $('#bw-basket-container');


      $(qunaitityBtns).prop('disabled', true);


      try {


        var itemtodrag = $(this).parent().parent().parent().parent().eq(0);


        var isDesktop = window.matchMedia(
          "(min-width: 768px)").matches;
        var duration = isDesktop ? 0.5 : 3;
        var ease = isDesktop ? "power2.easeOut" : "power1.easeOut";

        if (itemtodrag) {
          var imgclone = itemtodrag.clone()
            .offset({
              top: itemtodrag.offset().top,
              left: itemtodrag.offset().left
            })
            .css({
              'opacity': '0.9',
              'position': 'absolute',
              'z-index': '100',
              'background': '#fff',
              'padding': '20px'
            });
          imgclone.appendTo($('body'));

          let targetPosition = {
            top: cart.offset().top,
            left: cart.offset().left,
            width: 379,
            height: 100,
            opacity: 0
          };

          if (!isDesktop) {
            imgclone.css({
              'width': itemtodrag.width(),
            });
            targetPosition.top = cart.offset().top + cart.height();
          }


          gsap.to(imgclone, {
            duration: duration,
            ease: ease,
            ...targetPosition,
            boxShadow: "0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)",
            onComplete: () => {
              imgclone.remove();
            }
          });


          await cm.ajaxAddToBasket(data, updateBasket);


          imgclone.animate({
            'height': 0
          }, function() {
            $(this).detach();
          });
        }




        await updateBasketWithFreshData(updateQuantityButtons);
      } catch (error) {
        console.error("Error adding to basket:", error);
      } finally {

        $(qunaitityBtns).prop('disabled', false);
      }

    });



    $('.btn-number-circle').click(async function() {
        var $this = $(this);
        var type = $(this).data('type');
        var ctr = $(this).data('ctr');
        var inputField = $('#shop-item-amount' + ctr);
        var id = $(this).data('ticket-id');
        var partner_id = $(this).data('insurance-partner-id');
        var currentValue = parseInt(inputField.val(), 10) ||
          1; // Ensure currentValue is at least 1
        var newValue = currentValue;
        var variantId = $(this).data('variant-id');
        var productType = $(this).data('product-type');
        var addToBasketBtn = $(this).closest('.shop-item-cont').find('.shop-add-to-basket');
        var ticketName = $(this).data('ticket-name');
        var itemCounter = $(this).data('item-counter'); // Get item_counter value

        // TURN OFF THE BUTTON 
        $(this).prop('disabled', true);

        if (variantId) {

          currentValue = 0;
          $(`#shop-item-amount${ctr}[data-ticket-id="${id}"]`).each(function() {
          currentValue += parseInt($(this).val(), 10) || 0; // Sum up the values
        });
    }

    if (type === 'minus') {

      newValue = (currentValue > 1) ? currentValue - 1 : 1; // Don't go below 1





      var latestTicket = findLatestMatchingItem(productType, id, partner_id, ticketName, variantId);

      if (latestTicket) {

        var id_basket = latestTicket.id_basket;
        try {
          await ajaxDelFromBasket(id_basket);
          await updateBasketWithFreshData(updateQuantityButtons);
        } catch (error) {
          console.error("Error removing from basket:", error);
        }
      }
      setTimeout(() => $(this).prop('disabled', false), 100);

    } else if (type === 'plus') {


      newValue = currentValue + 1;


      if (variantId) {
        $(this).closest('.variant-item').find(
          'button.shop-add-to-basket[data-variant-id="' + variantId +
          '"]').click();
      } else {
        $(this).closest('.shop-item-cont').find('button.shop-add-to-basket').click();
      }

      inputField.val(newValue);
      setTimeout(() => $(this).prop('disabled', false), 100);

    }

  });




  // Function to check if a ticket exists in the basket data
  function ticketExistsInBasket(ticketId, productType, variantId) {

    return basketFreshData[productType] && basketFreshData[productType].some(function(item) {
      var productFound;
      var variantMatch;

      productFound = item.main_product.identyfikator == ticketId;
      variantMatch = !variantId || item.main_product.bsid ==
        variantId;


      if (!productFound && productType == 'ubezpieczenie') {
        // var name = $(this).data('insurance-ubezpieczyciel');

        return item.main_product.partner_id == ticketId;
      }
      if (!productFound && productType == 'kupon') {
        // var name = $(this).data('insurance-ubezpieczyciel');
        variantMatch = !variantId || item.main_product.kwota_brutto ==
          variantId;

        return item.main_product.id_emisji == ticketId && variantMatch;
      }


      return productFound && variantMatch;
    });
  }


  // Show/hide quantity buttons based on basket data
  async function updateQuantityButtons() {
    $('button.shop-add-to-basket').each(function() {
      var ticketId = $(this).data('ticket-id');
      var variantId = $(this).data('variant-id');
      var productWrapper = $(this).closest('.shop-item-cont');
      var addToBasketBtn = productWrapper.find('.btn-number-circle').first();
      var productType = addToBasketBtn.data('product-type');
      var ctrNumber = addToBasketBtn.data('ctr');
      var quantitySelector = $(this).closest('.shop-item-cont').find(
        '.quantity-selector');
      var ticketName = $(this).data('ticket-name');



      if (variantId) {
        productWrapper = $(this).closest('.variant-item');
        quantitySelector = productWrapper.find('.quantity-selector');
      }

      var inputField = quantitySelector.find(
        'input.input-number');




      if (productType == 'ubezpieczenie') {
        ticketId = $(this).data('insurance-partner-id');

      }
      if (productType == 'kupon') {
        ticketId = $(this).data('id-emisji');

      }


      if (ticketExistsInBasket(ticketId, productType, variantId)) {


        var orderQuantity = basketFreshData[productType].filter(function(item) {
          if (productType == 'kupon') {

            var foundItem = item.main_product.id_emisji == ticketId && item.main_product.kwota_brutto ==
              variantId;
            return foundItem;
          }

          if (productType == 'ubezpieczenie') {

            return item.main_product.partner_id == ticketId;
          }
          if (productType == 'produkt_wirtualny') {

            return ((item.main_product.identyfikator === ticketId) && (item
              .main_product.nazwa ==
              ticketName));
          }


          var variantMatch = !variantId || item.main_product.bsid == variantId;
          var foundItem = item.main_product.identyfikator == ticketId &&
            variantMatch &&
            item.main_product.nazwa == ticketName;



          return foundItem
        }).length;


        // Update the input field with the order quantity
        inputField.val(orderQuantity);
        $(this).hide();



        quantitySelector.first().show();



        showCollapse($(this))

      } else {


        $(this).show();
        quantitySelector.hide();


        // Delay the visibility check using setTimeout
        setTimeout(() => {
          const anyQuantitySelectorVisible = $(this).closest('.shop-item-cont')
            .find(
              '.quantity-selector:visible').length;


          if (!anyQuantitySelectorVisible) {
            hideCollapse(ctrNumber);

          }
        }, 300);



      }
    });
  }

  function showCollapse(button) {
    const collapse = $(button).closest('.variant-item').parent('.collapse');
    // Ensure the collapse stays open until after updates
    collapse.on('shown.bs.collapse', function() {
      const addToBasketBtn = $(button).closest('.shop-item-cont').find('.shop-add-to-basket-collapse')
        .first();
      addToBasketBtn.addClass('button-collapsed');

      // Disable hiding until updates are done
      collapse.off('hidePrevented.bs.collapse');
      collapse.on('hidePrevented.bs.collapse', function(e) {
        e.preventDefault();
      });
    });
    collapse.collapse('show');
  }

  function hideCollapse(ctrNumber) {
    const ticketVariantsContainer = $(`#ticketVariants${ctrNumber}`);

    // Check if the collapse is already hidden
    if (!ticketVariantsContainer.hasClass('in')) {
      return;
    }
    // Prevent hiding if any quantity selector is visible
    setTimeout(() => {
      const anyQuantitySelectorVisible = ticketVariantsContainer.find('.quantity-selector:visible').length;
      if (anyQuantitySelectorVisible === 0) {
        ticketVariantsContainer.collapse('hide');
        const addToBasketBtn = ticketVariantsContainer.closest('.shop-item-cont').find(
          '.shop-add-to-basket-collapse').first();
        addToBasketBtn.removeClass('button-collapsed');
        ticketVariantsContainer.collapse('hide');
      }
    }, 300);


  }




  $(document).on('click', '#removepromo', function(ev) {
    ev.preventDefault();
    if (!confirm('Chcesz usunąć promocję z zamówienia?')) {
      return false;
    }

    $.get('/order/removepromo', function(resp) {
      if (resp.status === false) {
        console.error(resp.error);
        return;
      }
      $('#promoPanel').remove();
    });
  });

  function updateBasket(response) {
    if (response['ok']) {
      cm.ajaxGetBasketCont('shop');



    } else {
      cm.appendError(false, response['error']);
    }
  }

  $('#btn-cancel-payment').click(function() {

    showConfirmationDialog(function(response) {
      if (response) {
        localStorage.removeItem(
          'user-no-login');
        cm.removeBasketData();

      }

    });
  });


  // Helper function to find a matching ticket
  function findMatchingTicket(tickets, id, partner_id, ticketName, variantId) {
    return tickets.find(ticket => {
      const productMatch = ticket.main_product.identyfikator == id || ticket.main_product
        .partner_id ==
        partner_id;
      const nameMatch = ticket.main_product.nazwa === ticketName;
      const variantMatch = !variantId || ticket.main_product.bsid == variantId;
      return productMatch && nameMatch && variantMatch;
    });
  }

  async function ajaxDelFromBasket(id_basket, callback) {


    try {
      const response = await $.ajax({
        type: 'POST',
        url: siteUrl + 'order/ajaxdelfrombasket',
        data: { 'id_basket': id_basket },
        dataType: 'json',
      });

      if (typeof response['error'] !== 'undefined') {
        cm.appendError($basketError, response['error']);
        await cm.ajaxGetBasketCont('shop');
        return false;
      }

      if (typeof response['ok'] !== 'undefined') {
        await cm.ajaxGetBasketCont('shop');
        if (typeof callback === 'function') {
          callback();
        }
        return true;
      }
    } catch (error) {
      console.error('Wystąpił błąd: ' + error.statusText);
    }
  }




  var params = {{$data.params|json_encode}};


  // $('#shopSummaryReturn, .navbar-logo').click(function(e) {
  //   e.preventDefault();
  //   cc.setReturnToShopFlag();
  // });


  // (async function() {
  //  try {

  //     var url = await cc.generateDynamicConfigUrl();



  //    var siteUrl = '{{$smarty.const.SITE_URL}}'; 
  //   var fullUrl = `${siteUrl}${url}`; 
  //    var dynamicLink = $('#btn-return-back');
  //   if (dynamicLink.length && fullUrl) {
  //     dynamicLink.attr('href', fullUrl);
  //   }
  // } catch (error) {
  //   console.error("Error generating dynamic config URL:", error);
  // }
  // })();









  $('.horizontal-scroll-tabs').on('scroll', function() {
    const $this = $(this);
    const scrollLeft = $this.scrollLeft();
    const scrollWidth = $this[0].scrollWidth;
    const clientWidth = $this.width();

    if (scrollLeft === 0) {
      // Scrolled to the beginning
      gsap.to($this, {
        duration: 0.3,
        ease: "power1.out",
        x: 10,
        onComplete: () => gsap.to(
          $this, { duration: 0.3, ease: "power1.out", x: 0 })
      });
    } else if (scrollLeft + clientWidth >= scrollWidth) {

      gsap.to($this.children(), {
        duration: 0.3,
        ease: "power1.out",
        x: -10,
        onComplete: () => gsap.to($this
          .children(), { duration: 0.3, ease: "power1.out", x: 0 })
      });
    }
  });


  $('.input-number').attr('disabled', 'disabled');

  $('.shop-add-to-basket-collapse').click(async function() {
    const $button = $(this);
    const ctr = $button.data('ctr');
    const $variantsContainer = $(`#ticketVariants${ctr}`);
    
    // Znajdź i zwiń otwarty panel informacyjny dla tego vouchera
    const $infoPanel = $button.closest('.shop-item-cont').find(`#voucher-notes-${$button.data('ticket-id')}`);
    if ($infoPanel.hasClass('in')) {
        $infoPanel.collapse('hide');
    }
    
    // Reszta istniejącego kodu dla obsługi vouchera z atrakcjami
    if ($variantsContainer.hasClass('attractions-container')) {
        // Inicjalizacja tablicy wybranych atrakcji
        if (!selectedAttractions[ctr]) {
            selectedAttractions[ctr] = [];
        }
        
        // Przygotuj dane vouchera podstawowego
        const voucherData = {
            identyfikator: $(this).data('ticket-id'),
            parentid: $(this).closest('.shop-item-cont').find('input#shop-item-parentid' + ctr).val(),
            bsid: $(this).closest('.shop-item-cont').find('select#shop-item-variant' + ctr).val()
        };

        // Pokaż/ukryj podsumowanie w zależności od wybranych atrakcji
        const $summary = $variantsContainer.find('.grouped-variants-summary');
        if (selectedAttractions[ctr] && selectedAttractions[ctr].length > 0) {
            $summary.show();
        } else {
            $summary.hide();
        }

        // Aktualizuj widok
        updateSelectedAttractionsView($variantsContainer, ctr);
        
        // Podmiana nazwy dla pierwszej atrakcji (Elka) - TYLKO dla kontenerów z atrakcjami
        setTimeout(() => {
            const $firstVariantName = $variantsContainer.find('.variant-item').first().find('.variant-name');
            const $firstVariantHeader = $variantsContainer.find('.variant-item').first().find('h4');
            
            $firstVariantName
                .html("Kolej Linowa<br>'Elka'")
                .css({
                    'line-height': '1.2',
                    'font-size': '14px',
                    'display': 'block',
                    'width': '150px',
                    'white-space': 'pre-line',
                    'word-break': 'break-word',
                    'padding-top': '8px'
                });
                
            $firstVariantHeader
                .text("Kolej Linowa 'Elka'");
        }, 100);
    }
    
    // Przełącz klasę przycisku
    $(this).toggleClass('button-collapsed');
    
    // Pokaż/ukryj panel wyboru atrakcji
    $variantsContainer.collapse('toggle');
});





  ////////////////////////////////////////////////////////////////////////////////
  });


  $(document).ready(function() {
    function handleCountdown(expirationDate, id, isDiscount) {
      // Konwertuj datę wygaśnięcia i odejmij jeden dzień
      var endDate = new Date(expirationDate);
      endDate.setDate(endDate.getDate());
      endDate.setHours(23, 59, 59, 999);

      var thirtyOneDays = 31 * 24 * 60 * 60 * 1000;
      var fourteenDays = 14 * 24 * 60 * 60 * 1000;
      var sevenDays = 7 * 24 * 60 * 60 * 1000;
      var oneMinute = 60 * 1000;

      var expirationDateSpan = $(`div.ticket-expiration-date[data-id=${id}]`);
      var originalText = expirationDateSpan.find('small').text();

      function setCountdown() {
        var now = new Date();
        var distance = endDate.getTime() - now.getTime();

        if (distance < 0) {
          expirationDateSpan.hide();
          return;
        }

        // Obliczanie pozostałego czasu
        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));

        // Ustawianie koloru na podstawie pozostałego czasu
        var color;
        if (distance > thirtyOneDays) {
          // Extract the date from the original text
          const textParts = originalText.split('do'); // Split on 'do' word
          const dateStr = textParts[1].trim(); // Get the date part

          // Parse and format the date
          const date = new Date(dateStr);
          const formattedDate = date.toISOString().split('T')[0]; // Get YYYY-MM-DD format

          // Reconstruct the text with formatted date
          const updatedText = `${textParts[0]}do ${formattedDate}`;

          // Update the span
          expirationDateSpan.html(`<span class=""><small>${updatedText}</small></span>`);
          return;
        } else if (distance > fourteenDays) {
          color = 'green';
        } else if (distance > sevenDays) {
          color = 'orange';
        } else {
          color = 'red';
        }

        // Pokazuj licznik tylko jeśli zostało mniej niż 31 dni
        var countdownText = (isDiscount ? 'Rabat wygaśnie za ' : 'Oferta wygaśnie za ') +
          days + "d " +
          (hours < 10 ? "0" + hours : hours) + "h " +
          (minutes < 10 ? "0" + minutes : minutes) + "m";

        expirationDateSpan.css('color', color)
        .html(`<span class=""><small>${countdownText}</small></span>`);
      }

      // Pierwsze wywołanie
      setCountdown();

      // Aktualizuj co minutę
      var interval = setInterval(setCountdown, oneMinute);

      // Czyszczenie interwału gdy element zostanie usunięty z DOM
      expirationDateSpan.on('remove', function() {
        clearInterval(interval);
      });
    }

    const ticketExpirationContainers = $('.ticket-expiration-date')
    $(ticketExpirationContainers).each(function() {

      var sprzedazDo = $(this).data('sprzedaz-do');
      var discountEnds = $(this).data('discount-ends');
      var id = $(this).data('id');
      var rabatValue = parseInt($(this).data('rabat-value'));
      var expirationDate = rabatValue > 0 ? new Date(discountEnds).getTime() : (new Date(sprzedazDo).getTime());


      if (expirationDate) {
        handleCountdown(expirationDate, id, rabatValue > 0);
      }
    });
  });



  $('.shop-item-variant-button').each(function() {
    // Get the corresponding select element
    var targetSelect = $($(this).data('target'));

    // Check if this button's value matches the select's value
    if ($(this).val() === targetSelect.val()) {
      $(this).addClass('active');
    } else {
      $(this).removeClass('active');
    }
  }).click(function() {
    // Remove 'active' class from all buttons in the same group
    $(this).siblings('.shop-item-variant-button').removeClass('active');

    // Add 'active' class to the clicked button
    $(this).addClass('active');

    var targetSelect = $($(this).data('target'));
    var selectedValue = $(this).val();

    targetSelect.val(selectedValue).trigger('change');
  });

  // Dodaj ten kod na końcu istniejącego skryptu

  $(document).ready(function() {
    var currentOpenPopup = null;


    $(document).on('click', function(e) {
      if (currentOpenPopup && !$(e.target).closest('.info-panel-wrapper').length) {
        $(currentOpenPopup).collapse('hide');
        currentOpenPopup = null;
      }
    });

    $('.info-panel-wrapper').on('click', function(e) {
      e.stopPropagation();
    });

  });

  // Sprawdź długość tekstu w każdym panelu informacyjnym
  $('.ticket-info-collapse-container').each(function() {
    const content = $(this).text();
    if (content.length > 900) {
      $(this).addClass('scrollable');
    } else {
      $(this).addClass('non-scrollable');
    }
  });

  // Dostosuj wysokość przy zmianie rozmiaru okna
  $(window).on('resize', function() {
    $('.ticket-info-collapse-container, .info-panel-wrapper-shop').each(function() {
      if ($(window).width() > 768) {
        const containerHeight = $(window).height() * 0.6;
        const containerWidth = $(window).width() * 0.4;
        const content = $(this).find('.padding20');
        const contentHeight = content.prop('scrollHeight');

        $(this).css({
          'min-height': Math.min(contentHeight + 40, 560) + 'px',
          'max-height': '560px',
          'width': Math.min(640, containerWidth) + 'px',
          'min-width': '300px',
          'height': 'auto',
          'margin-left': '0'
        });

        content.css({
          'width': '100%',
          'height': 'auto'
        });
      }
    });
  }).trigger('resize');

  // Obsługa wybranych wariantów
  $(document).ready(function() {
    // Obiekt do przechowywania wybranych wariantów dla każdego vouchera
    const selectedVariants = {};

    // Obsługa kliknięcia przycisku "Wybierz" dla wariantu
    $(document).on('click', '.add-variant-to-group', function() {
      const $button = $(this);
      const ctr = $button.data('ctr');
      const variantId = $button.data('variant-id');
      const variantName = $button.data('variant-name');
      const variantPrice = $button.data('variant-price');
      const ticketName = $button.data('ticket-name');
      const variantIdentyfikator = $button.data('variant-identyfikator');
      const variantParentId = $button.data('variant-parent-id');
      const $confirmButton = $button.closest('.ticket-variants-container')
        .find('.confirm-variants-btn');

      // Inicjalizacja tablicy dla danego vouchera jeśli nie istnieje
      if (!selectedVariants[ctr]) {
        selectedVariants[ctr] = [];
      }

      // Sprawdź czy wariant już został wybrany
      const isVariantSelected = selectedVariants[ctr].some(v => v.variantId === variantId);

      if (!isVariantSelected) {
        // Dodaj wariant do wybranych
        selectedVariants[ctr].push({
          variantId,
          variantName: "Kolej Linowa 'Elka'",
          variantPrice,
          ticketName,
          variantIdentyfikator,
          variantParentId
        });

        // Aktualizuj widok i atrybut data-selected-items
        updateSelectedVariantsView(ctr);

        // Aktualizuj atrybut data-selected-items
        const selectedIds = selectedVariants[ctr].map(v => v.variantId).join(',');
        $confirmButton.attr('data-selected-items', selectedIds);
      }
    });

    // Funkcja aktualizująca widok wybranych wariantów
    function updateSelectedVariantsView(ctr) {
      const $container = $(`#grouped-variants-list-${ctr}`);
      const $confirmButton = $container.closest('.grouped-variants-container')
        .find('.confirm-variants-btn');

      $container.empty();

      // Przygotuj string z ID wybranych wariantów
      const selectedIds = selectedVariants[ctr].map(v => v.variantId).join(',');

      // Aktualizuj atrybut data-selected-items
      $confirmButton.attr('data-selected-items', selectedIds);

      selectedVariants[ctr].forEach((variant, index) => {
        const attractionName = "Kolej Linowa 'Elka'";
        const variantHtml = `
<div class="selected-variant-item" data-variant-id="${variant.variantId}">
                <div class="selected-variant-info">
<span class="selected-variant-name">${attractionName}</span>
                    <div class="selected-variant-price">
<span class="variant-base-price">${variant.variantPrice}</span>
                    </div>
                </div>
                <button type="button" class="btn btn-danger btn-sm remove-variant" 
data-ctr="${ctr}" 
data-variant-id="${variant.variantId}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        $container.append(variantHtml);
      });

      // Pokaż/ukryj przycisk potwierdzenia w zależności od liczby wybranych wariantów
      if (selectedVariants[ctr].length > 0) {
        $confirmButton.closest('.grouped-variants-summary').show();
      } else {
        $confirmButton.closest('.grouped-variants-summary').hide();
        // Wyczyść atrybut data-selected-items gdy nie ma wybranych elementów
        $confirmButton.removeAttr('data-selected-items');
      }
    }

    // Obsługa usuwania wariantu
    $(document).on('click', '.remove-variant', function() {
      const $button = $(this);
      const ctr = $button.data('ctr');
      const variantId = $button.data('variant-id');
      const $confirmButton = $button.closest('.grouped-variants-container')
        .find('.confirm-variants-btn');

      // Usuń wariant z tablicy wybranych
      selectedVariants[ctr] = selectedVariants[ctr].filter(v => v.variantId !== variantId);

      // Aktualizuj widok i atrybut data-selected-items
      updateSelectedVariantsView(ctr);

      // Jeśli nie ma już wybranych wariantów
      if (selectedVariants[ctr].length === 0) {
        $confirmButton.closest('.grouped-variants-summary').hide();
        $confirmButton.removeAttr('data-selected-items');
      } else {
        // Aktualizuj atrybut data-selected-items
        const remainingIds = selectedVariants[ctr].map(v => v.variantId).join(',');
        $confirmButton.attr('data-selected-items', remainingIds);
      }
    });

    // Obsługa przycisku "Zatwierdź wybrane"
    $(document).on('click', '.confirm-variants-btn', async function() {
      const $button = $(this);
      const ctr = $button.data('ctr');
      const selectedIds = $button.attr('data-selected-attractions');

      const voucherIdentyfikator = $button.data('ticket-id');
      const voucherBsid = $button.data('voucher-bsid');
      const voucherParentId = $button.data('parent-id');
      const $variantsContainer = $button.closest('.ticket-variants-container');
      const $collapseButton = $button.closest('.shop-item-cont').find('.shop-add-to-basket-collapse');

     

      if (selectedAttractions[ctr] && selectedAttractions[ctr].length > 0) {

        $button.prop('disabled', true).addClass('loading');
        /* 
                basket {
                "p_type": "voucher_elka",
                "identyfikator": "1234567890",
                "bsid": "1234567890",
                "amount": 1,
                "parentid": "1234567890",
                "selected_attractions_ids": [
                  "1234567890",
                  "1234567890"
                ]
              }
              */

        const data = {
          p_type: 'voucher',
          identyfikator: voucherIdentyfikator,
          bsid: voucherBsid,
          amount: 1,
          parentid: voucherParentId,
          selected_attractions_ids: selectedIds
        };

       



        var cart = $('#bw-basket-container');





        try {


          var itemtodrag = $(this).parent().parent().parent().parent().eq(0);


          var isDesktop = window.matchMedia(
            "(min-width: 768px)").matches;
          var duration = isDesktop ? 0.5 : 3;
          var ease = isDesktop ? "power2.easeOut" : "power1.easeOut";

          if (itemtodrag) {
            var imgclone = itemtodrag.clone()
              .offset({
                top: itemtodrag.offset().top,
                left: itemtodrag.offset().left
              })
              .css({
                'opacity': '0.9',
                'position': 'absolute',
                'z-index': '100',
                'background': '#fff',
                'padding': '20px'
              });
            imgclone.appendTo($('body'));

            let targetPosition = {
              top: cart.offset().top,
              left: cart.offset().left,
              width: 379,
              height: 100,
              opacity: 0
            };

            if (!isDesktop) {
              imgclone.css({
                'width': itemtodrag.width(),
              });
              targetPosition.top = cart.offset().top + cart.height();
            }


            gsap.to(imgclone, {
              duration: duration,
              ease: ease,
              ...targetPosition,
              boxShadow: "0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)",
              onComplete: () => {
                imgclone.remove();
              }
            });


            await cm.ajaxAddToBasket(data, updateBasket);


            imgclone.animate({
              'height': 0
            }, function() {
              $(this).detach();
            });
          }









        } catch (error) {
          console.error("Error adding to basket:", error);
        }

        selectedAttractions[ctr] = [];
        $button.removeAttr('data-selected-attractions');
        updateSelectedAttractionsView($variantsContainer, ctr);
        $button.prop('disabled', false).removeClass('loading');
        $(`#ticketVariants${ctr}`).collapse('hide');
        await updateBasketWithFreshData(updateQuantityButtons);
      } else {
      }
    });

    // Dodajmy też obsługę resetowania przy zamknięciu kontenera
    $('[id^=ticketVariants]').on('hidden.bs.collapse', function() {
      const ctr = this.id.replace('ticketVariants', '');
      const $container = $(this);

      // Reset stanu tylko jeśli nie ma wybranych atrakcji w koszyku
      if (!selectedAttractions[ctr] || selectedAttractions[ctr].length === 0) {
        // Reset przycisków
        $container.find('.add-to-cart[data-attraction-id]').each(function() {
          $(this).text('Wybierz')
            .removeClass('selected btn-danger')
            .addClass('btn-primary');
        });

        // Reset podsumowania
        const $summary = $container.find('.grouped-variants-summary');
        if ($summary.length) {
          $summary.hide();
        }

        // Reset przycisku collapse
        $container.closest('.shop-item-cont')
          .find('.shop-add-to-basket-collapse')
          .removeClass('button-collapsed');
      }
    });

    // Funkcja pokazująca powiadomienie o dodaniu do koszyka
    function showCartNotification(message) {
      const notification = $(`
      <div class="cart-notification" style="position: fixed; bottom: 20px; right: 20px; color: white; padding: 15px; border-radius: 5px; z-index: 1050; opacity: 0; transition: opacity 0.3s ease-in-out;">
<i class="fas fa-check-circle"></i> ${message}
      </div>
    `).appendTo('body');

      setTimeout(() => notification.css('opacity', 1), 10);

      setTimeout(() => {
        notification.css('opacity', 0);
        setTimeout(() => notification.remove(), 350);
      }, 3000);
    }
  });

  $(document).ready(function() {


    $(document).on('click', '.add-to-cart[data-attraction-id]', function(e) {
      e.preventDefault();
      const $button = $(this);
      const $variantsContainer = $button.closest('.ticket-variants-container');
      const ctr = $button.data('ctr');
      const attractionId = $button.data('attraction-id');
      const ticketId = $button.data('ticket-id');
      const parentId = $button.data('variant-parent-id');
      const $variantItem = $button.closest('.variant-item');
      const attractionName = "Kolej Linowa 'Elka'";
      const attractionPrice = $variantItem.find('.variant-base-price').text().trim();
      const promoValue = parseFloat($variantItem.find('.label-primary').text().replace(/[^\d.,]/g, '')) || 0;

      selectedAttractions[ctr] = selectedAttractions[ctr] || [];
      const selectedIndex = selectedAttractions[ctr].findIndex(a => a.attractionId === attractionId);

      if (selectedIndex > -1) {
        // Usuń atrakcję
        selectedAttractions[ctr].splice(selectedIndex, 1);
        $button.text('Wybierz')
               .removeClass('selected btn-danger')
               .addClass('btn-primary');
      } else {
        // Dodaj atrakcję
        selectedAttractions[ctr].push({
          attractionId,
          attractionName,
          attractionPrice,
          promoValue,
          ticketId,
          parentId
        });
        $button.text('Usuń')
               .addClass('selected btn-danger')
               .removeClass('btn-primary');
      }

      // Aktualizuj przycisk "Zatwierdź wybrane"
      const $confirmButton = $variantsContainer.find('.confirm-variants-btn');
      const selectedIds = selectedAttractions[ctr].map(a => a.attractionId).join(',');
      $confirmButton.attr('data-selected-attractions', selectedIds);

      updateSelectedAttractionsView($variantsContainer, ctr);
    });

    function updateSelectedAttractionsView($variantsContainer, ctr) {
      const $summary = $variantsContainer.find('.grouped-variants-summary');
      const $confirmButton = $variantsContainer.find('.confirm-variants-btn');
      const $selectionMessage = $confirmButton.siblings('.attraction-selection-message');
      const basePromoValue = parseFloat($variantsContainer.closest('.shop-item-cont')
        .find('.basic-discount-text').text().match(/\d+/)) || 0;

      const currentSelection = selectedAttractions[ctr] || [];

      // Aktualizuj stan przycisku i komunikatu
      if (currentSelection.length === 0) {
        $confirmButton.addClass('disabled').prop('disabled', true);
        $selectionMessage.show();
      } else {
        $confirmButton.removeClass('disabled').prop('disabled', false);
        $selectionMessage.hide();
      }

      // Obliczanie cen i rabatów
      // 1. Pobierz cenę podstawową vouchera
      const baseVoucherPrice = parseFloat($variantsContainer.closest('.shop-item-cont')
          .find('.shop-item-price .bold').text().replace(/[^\d.,]/g, '').replace(',', '.')) || 0;

      // 2. Oblicz sumę cen wszystkich atrakcji
      let attractionsTotal = 0;
      currentSelection.forEach(attraction => {
          const price = parseFloat(attraction.attractionPrice.replace(/[^\d.,]/g, '').replace(',', '.'));
          attractionsTotal += price;
      });

      // 3. Oblicz całkowitą sumę zamówienia przed rabatami
      const totalBeforeDiscount = baseVoucherPrice + attractionsTotal;

      // 4. Oblicz sumę wszystkich rabatów
      let totalDiscountPercentage = basePromoValue; // Zaczynamy od rabatu podstawowego

      // 5. Dodaj rabaty z atrakcji
      currentSelection.forEach(attraction => {
          if (attraction.promoValue) {
              totalDiscountPercentage += attraction.promoValue;
          }
      });

      // 6. Oblicz końcową wartość rabatu
      const totalDiscountAmount = (totalBeforeDiscount * totalDiscountPercentage) / 100;

      // 7. Oblicz cenę końcową
      const finalTotal = totalBeforeDiscount - totalDiscountAmount;

      // Aktualizuj podsumowanie
      if ($summary.length) {
          $summary.find('.base-price').text(totalBeforeDiscount.toFixed(2) + ' zł');
          $summary.find('.discount').html(
            `-${totalDiscountAmount.toFixed(2)} zł <span style="color: #5cb85c;">(${totalDiscountPercentage}%)</span>`
        );
          $summary.find('.final-price').text(finalTotal.toFixed(2) + ' zł');
          $summary.show();

          // Dodaj informację o rabatach
      }
    }

    $(document).on('click', '.remove-attraction', function() {
      const $button = $(this);
      const ctr = $button.data('ctr');
      const attractionId = $button.data('attraction-id');
      const $variantsContainer = $button.closest('.ticket-variants-container');

      if (selectedAttractions[ctr]) {
        selectedAttractions[ctr] = selectedAttractions[ctr].filter(a => a.attractionId !== attractionId);
        updateSelectedAttractionsView($variantsContainer, ctr);
      }
    });

    function showCartNotification(message) {
      const notification = $(`
      <div class="cart-notification" style="position: fixed; bottom: 20px; right: 20px;  color: white; padding: 15px; border-radius: 5px; z-index: 1050; opacity: 0; transition: opacity 0.3s ease-in-out;">
<i class="fas fa-check-circle"></i> ${message}
      </div>
    `).appendTo('body');

      setTimeout(() => notification.css('opacity', 1), 10);

      setTimeout(() => {
        notification.css('opacity', 0);
        setTimeout(() => notification.remove(), 350);
      }, 3000);
    }
  });

  $(document).ready(function() {
    // Inicjalizacja stanu wszystkich przycisków "Zatwierdź wybrane" i komunikatów
    $('.confirm-variants-btn').each(function() {
        const $button = $(this);
        const $selectionMessage = $button.siblings('.attraction-selection-message');
        const ctr = $button.data('ctr');
        const currentSelection = selectedAttractions[ctr] || [];
        
        if (currentSelection.length === 0) {
            $button.addClass('disabled').prop('disabled', true);
            $selectionMessage.show();
        } else {
            $selectionMessage.hide();
        }
    });
  });

  $(document).ready(function() {
    // Dodaj overlay jeśli nie istnieje
    if ($('#popup-overlay').length === 0) {
      $('body').append('<div id="popup-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1049;"></div>');
    }

    let scrollPosition = 0;

    // Funkcja do blokowania scrollowania
    function lockScroll() {
      scrollPosition = window.pageYOffset;
      $('body').css({
        overflow: 'hidden',
        position: 'fixed',
        top: `-${scrollPosition}px`,
        width: '100%'
      });
      $('#popup-overlay').fadeIn(200);
    }

    // Funkcja do odblokowywania scrollowania
    function unlockScroll() {
      $('body').css({
        overflow: '',
        position: '',
        top: '',
        width: ''
      });
      window.scrollTo(0, scrollPosition);
      $('#popup-overlay').fadeOut(200);
    }

    // Obsługa kliknięcia w ikonę info
    $(document).on('click', '.shop-main-info-container a[data-toggle="collapse"]', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const targetId = $(this).attr('href');
      const $target = $(targetId);
      
      // Sprawdź czy to info o atrakcji (po ID lub klasie kontenera)
      const isAttractionInfo = targetId.includes('attraction-notes');

      // Zamknij inne otwarte popupy
      $('.ticket-info-collapse-container.in').not(targetId).collapse('hide');

      // Przełącz aktualny popup
      $target.collapse('toggle');

      // Zarządzaj scrollowaniem TYLKO dla info o atrakcjach na mobile
      if (window.innerWidth <= 768 && isAttractionInfo) {
        if (!$target.hasClass('in')) {
          lockScroll();
        } else {
          unlockScroll();
        }
      }
    });

    // Zamykanie po kliknięciu poza popupem
    $(document).on('click', function(e) {
      if (!$(e.target).closest('.ticket-info-collapse-container').length && 
          !$(e.target).closest('.shop-main-info-container a[data-toggle="collapse"]').length) {
        
        // Sprawdź czy aktualnie otwarty popup to info o atrakcji
        const openPopup = $('.ticket-info-collapse-container.in');
        const isAttractionInfo = openPopup.length && openPopup.attr('id').includes('attraction-notes');
        
        if (openPopup.length && window.innerWidth <= 768 && isAttractionInfo) {
          unlockScroll();
        }
        openPopup.collapse('hide');
      }
    });

    // Obsługa zamykania przy collapse
    $('.ticket-info-collapse-container').on('hidden.bs.collapse', function() {
      const isAttractionInfo = this.id.includes('attraction-notes');
      if (window.innerWidth <= 768 && isAttractionInfo) {
        unlockScroll();
      }
    });
  });

  $(document).ready(function() {
    // Zmień nazwę TYLKO dla pierwszego vouchera z atrakcjami (nie dla wszystkich variant-item)
    $('.attractions-container .variant-item').first().find('.variant-name').text("Kolej Linowa 'Elka'");
    
    // Przy kliknięciu w przycisk, sprawdź czy to pierwsza atrakcja w kontenerze z atrakcjami
    $(document).on('click', '.add-to-cart[data-attraction-id]', function(e) {
        const $variantItem = $(this).closest('.variant-item');
        const $attractionsContainer = $(this).closest('.attractions-container');
        
        if ($attractionsContainer.length > 0) {
            const isFirstAttraction = $attractionsContainer.find('.variant-item').first().is($variantItem);
            
            if (isFirstAttraction) {
                const attractionName = "Kolej Linowa 'Elka'";
            } else {
                const attractionName = $variantItem.find('.variant-name').text().trim();
            }
        }
    });
  });

  $(document).ready(function() {
    // Dodaj style bezpośrednio do head
    $('head').append(`
        <style>
            .variant-name.elka-name {
                line-height: 1.2;
                display: flex;
                align-items: center;
                min-height: 40px;
                font-size: 14px;
            }
            
            h4.elka-name {
                line-height: 1.2;
                margin-top: 10px;
                margin-bottom: 10px;
            }
        </style>
    `);
  });
</script>
