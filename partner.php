<?php

/**
 * <PERSON><PERSON><PERSON> aplikacji ESKIPASS
 */


header('P3P:CP="IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT"');
header('Access-Control-Allow-Origin: *');

require_once("config.php");


if(file_exists('maintaining.tpl')) {
    echo file_get_contents('themes/offline.html');
    exit();
}

define('ESID_USE_TRANS_SID', false);
define('ESID_SESSION_FINGERPRINT', \App\Middleware\SessionsMiddleware::getSessionFingerPrint());

require_once(ENSETTINGSDIR."libs/Sessions.class.php");
require_once(ENSETTINGSDIR."libs/User.class.php");
require_once("application/App.php");

define('THEME', 'partner');
define('SCONTEXT', 'partner');
define('COMMONDIR','themes/common/');
define('THEMEDIR', 'themes/' . THEME);
define('DEFCONTROLLER', 'partner');

Sessions::activate();
User::activate();
$_SESSION['SCONTEXT'] = SCONTEXT;
$app = new App();
$app->runController();
$app->Render();
exit();