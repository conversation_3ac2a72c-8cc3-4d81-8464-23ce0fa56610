<?php
require_once('config.php');
require_once('application/_engine_v4_0/libs/Mymongo.class.php');
require_once('application/_engine_v4_0/libs/Database.class.php');

if ($argc == 1) {
    die('Add start argument. As second arg add update');
}

$commands = [
    'update'
];
$start = $argv[1];
if ($argc > 2) {
    $command = $argv[2];
} else {
    $command = 'update';
}

if ($start !== 'start') {
    die('RTFM');
}

if (!in_array($command, $commands)) {
    die('Bad command');
}

function isCli()
{
    if (defined('STDIN')) {
        return true;
    }

    if (empty($_SERVER['REMOTE_ADDR']) and !isset($_SERVER['HTTP_USER_AGENT']) and count($_SERVER['argv']) > 0) {
        return true;
    }

    return false;
}

updateFaktury::handle($command);

class updateFaktury
{

    public static function handle($command)
    {
        if(isCli()){
            $mongo = Mymongo::activate();

            $sql = Database::activate();
            $sqlData = $sql->select("SELECT * FROM " . TABLE_PREFIX . "faktury WHERE `nip` IS NULL");
            $updated = 0;
            foreach ($sqlData as $sqlItem)
            {
                $mongoItemData = $mongo->get_invoice($sqlItem['numer_fv']);
                $nip = $mongoItemData['nabywca']['NIP'];
                if(!empty($nip))
                {
                    $sql->query("UPDATE ". TABLE_PREFIX."faktury SET `nip`=". $nip . " WHERE `id` = ".$sqlItem['id']);
                    $updated++;
                }
            }
            echo "Updated " . $updated . " records (". count($sqlData) . " total)" . PHP_EOL;
        }
    }

    public static function confirm($message)
    {
        echo $message;
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        if (trim($line) != 'yes') {
            echo "ABORTING!\n";
            exit;
        }
        fclose($handle);
        echo "\n";
        echo "Thank you, continuing...\n";
    }

}

