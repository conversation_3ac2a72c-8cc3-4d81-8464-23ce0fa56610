<?php
require_once("config.php");

if (!isCli()) {
    abort(404);
}

define('ESID_USE_TRANS_SID', false);
define('ESID_SESSION_FINGERPRINT', md5(SALT));

require_once(ENSETTINGSDIR."libs/Sessions.class.php");
require_once(ENSETTINGSDIR."libs/User.class.php");
require_once("application/App.php");
Sessions::activate();

define('SCONTEXT','front');

define('THEME', df($sconfig->system->theme, 'energy'));
define('COMMONDIR','themes/common/');
define('THEMEDIR','themes/' . THEME);
define('SITE_URL',df($sconfig->system->site_url, ''));
define('THEMEURL',SITE_URL.THEMEDIR);
define('DEFCONTROLLER', 'order' );
define('LANG', 24 );

User::activate();

$app = new App(DEFCONTROLLER);
$commands = [
    'list',
    'process-delayed-orders',
    'resend-order-emails',
    'update-wrong-card-number',
    'send-emails',
    'truncate-exrej',
    'disable-old-coupon-emissions',
    'update-wrong-users',
    'remove-old-baskets',
];

$default_command = 'process-delayed-orders';
$command = $argv[1];
if (empty($command)) {
    echo 'Using default command' . PHP_EOL;
    $command = $default_command;
}


if (!in_array($command, $commands)) {
    echo 'Bad command' . PHP_EOL;
    die();
}

switch ($command) {
    case 'list':
        echo 'Lista komend:' . PHP_EOL;
        foreach($commands as $command) {
            echo '-- ' . $command . PHP_EOL;
        }
        break;
    case 'resend-order-emails':
        $run = new \App\Commands\ResendOrderEmailsCmd();
        $resp = $run->handle($app);
        break;

    case 'update-wrong-card-number':
        $run = new \App\Commands\UpdateWrongCardNumbersCmd();
        $resp = $run->handle($app);
        break;

    case 'send-emails':
        $run = new \App\Commands\SendEmails();
        $resp = $run->handle($app);
        break;

    case 'truncate-exrej':
        $run = new \App\Commands\TruncateExRejCmd();
        $resp = $run->handle($app);
        break;

    case 'disable-old-coupon-emissions':
        $run = new \App\Commands\DisableOldCouponEmissions();
        $resp = $run->handle($app);
        break;

    case 'update-wrong-users':
        $run = new \App\Commands\UpdateWrongUserIdCmd();
        $resp = $run->handle($app);
        break;

    case 'remove-old-baskets':
        $run = new \App\Commands\RemoveOldBaskets();
        $run->handle($app);
        break;

    default:
        $run = new \App\Commands\ProcessDelayedOrdersCmd();
        $resp = $run->handle($app);
        break;
}

echo $resp . PHP_EOL;
exit();
