<?php
$sconfig = json_decode(json_encode(parse_ini_file('.config.ini', true)), false);

global $sconfig;

define('VERSION', '5.6.0');
define('HTTPP',!empty($_SERVER['HTTPS']) ? 'https://' : 'http://' );

include 'application/_engine_v4_0/libs/helpers.php';

define('ENV',df($sconfig->system->env, 'loc'));
define('DEBUG',df($sconfig->system->debug, false));
error_reporting(intval(df($sconfig->system->error_reporting, E_ALL & ~E_NOTICE)));
ini_set('display_errors', df($sconfig->system->display_errors, 0));



define('BSAPIV', df($sconfig->system->bsapiv, 2));
//define('THEME', df($sconfig->system->theme, 'energy'));

define('ECONOMIC_CARD_NAME', df($sconfig->additionals->economic_card_name, 'default_img.jpg'));
define('MASTER_PASS', df($sconfig->additionals->master_pass, md5(random_bytes(32))));
define('PROCESS_ORDER', df($sconfig->additionals->process_order, 'sync'));
define('SITE_BD', df($sconfig->system->site_bd, '/'));
define('SALT', df($sconfig->system->salt, 'lk23j49871454.KP903rl/'));
define('TABLE_PREFIX', df($sconfig->mysql->table_prefix, 'ts_'));
define('DBDEBUG', df($sconfig->mysql->dbdebug, false));
define('VER', df($sconfig->system->ver, '4.0'));
define('MAILER', df($sconfig->email->mailer, 'log'));
$conf=array(
    'host' => df($sconfig->email->smtp_host, 'mail.e-skipass.pl'),
    'port' => df($sconfig->email->port, 25),
    'auth' => df($sconfig->email->auth, true),
    'username' => df($sconfig->email->smtp_user, '<EMAIL>'),
    'password' => df($sconfig->email->smtp_password, 'CpSDmk53'),
    'SMTPSecure' => df($sconfig->email->smtp_secure, '')
);
define('MAILER_CONF', serialize($conf));
define('MAILER_FROM', df($sconfig->email->from, '<EMAIL>'));
define('MAILER_NAME', df($sconfig->email->from_name, 'E-skipass'));
define('DEFAULT_EMAIL', df($sconfig->email->default_email, '<EMAIL>'));
define('MEDIATION_EMAIL', df($sconfig->mediation->email, '<EMAIL>'));
define('MEDIATION_EMAIL_SERVER', df($sconfig->mediation->smtp_host, 'mail.e-skipass.pl'));
define('MEDIATION_EMAIL_PASS', df($sconfig->mediation->smtp_password, '5aDMefVjk'));

/** FACEBOOK API */
define('FB_API_KEY', df($sconfig->fb->api_key, ''));
define('FB_SECRET', df($sconfig->fb->api_secret, ''));

/** Google Maps API */
define('GOOGLE_MAPS_API', df($sconfig->google->maps_api, 'AIzaSyBewS5gCtsl9LIjaH-xOXDFrXKhOlk8Fio'));

require_once 'application/config/db_config.php';

define('DP_URL', df($sconfig->dotpay->url, 'https://ssl.dotpay.pl/test_payment/'));
define('DP_ID', df($sconfig->dotpay->id, '723411'));
define('DP_PIN',  df($sconfig->dotpay->pin, 'sHEGInBQsSPUOSS1e1BUl8vzan80KcAL'));
define('DP_IP', df($sconfig->dotpay->ip, '************'));
define('DPCARD_CHANNEL', df($sconfig->dotpay->card_channel, 246));

/** ID sposoby platnosci */
define('PAYOFFLINE_ID', df($sconfig->payoffline->system_id, 92));
define('PAYWALLET_ID', df($sconfig->paywallet->system_id, 32));
define('PAYDP_ID', df($sconfig->dotpay->system_id, 40));
define('PAYBON_ID',  df($sconfig->paybon->system_id, 94));
define('PAYP24_ID',  df($sconfig->p24->system_id, 182));

define('QR_PROMO_SHORT_TIME', df($sconfig->additionals->qr_promo_short_time, 'PT5M'));
define('INVOICE_URL',df($sconfig->system->invoice_url, ''));
define('QR_PROMO_USE_HASH',df($sconfig->additionals->qr_promo_use_hash, 'false'));

/** 
 * System paths
 */
define('PROJECTDIR', __DIR__);
$engine_settings_dir= PROJECTDIR . "/application/_engine_v".str_replace(".","_",VER)."/";
$settings_dir= PROJECTDIR . "/application/website_v".str_replace(".","_",VER)."/";
define('SETTINGSDIR',$settings_dir);
define('ENSETTINGSDIR',$engine_settings_dir);


set_include_path(get_include_path().PATH_SEPARATOR.$settings_dir);
set_include_path(get_include_path().PATH_SEPARATOR.$engine_settings_dir);

date_default_timezone_set('Europe/Warsaw');

header('P3P:CP="IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT"');

require_once("vendor/autoload.php");

/** 
 * Autoloader
 */
spl_autoload_register(array('_loadClass','LoadEn'));

/**
 * 
 * @package Application
 */
class _loadClass {
    public static function LoadEn($cn) {
        if(is_readable(ENSETTINGSDIR . 'libs/' . $cn . '.class.php')){
            include_once (ENSETTINGSDIR . 'libs/' . $cn . '.class.php');
        }
    }
}

// PHPMailer

function PHPMailerAutoload($classname)
{

    $filename = ENSETTINGSDIR.'libs/'.'class.'.strtolower($classname).'.php';
    if (is_readable($filename)) {
        require $filename;
    }
}

if (version_compare(PHP_VERSION, '5.1.2', '>=')) {
    if (version_compare(PHP_VERSION, '5.3.0', '>=')) {
        spl_autoload_register('PHPMailerAutoload', true, true);
    } else {
        spl_autoload_register('PHPMailerAutoload');
    }
}