; this is an INI file
[system]
debug = false
env = loc
site_bd = "/"
salt = 'lk23j4987sg203)KP903(*&rl'
def_controller = welcome
def_redirect = account
display_errors = 1
error_reporting = E_ALL & ~E_NOTICE
admin_redirect = '?users&a=list&role=3'
site_url = 'http://epass.x-soft.it/'
invoice_url = 'http://invoice.epass.odyssey/'
ver = '4.0'

[email]
mailer = smtp
port = 25
smtp_host = mail.e-skipass.pl
smtp_user = <EMAIL>
smtp_password =
smtp_secure = ''
auth = false
from = '<EMAIL>'
from_name = 'E-Skipass'
default_email = '<EMAIL>

[mediation]
email = '<EMAIL>'
smtp_host = mail.e-skipass.pl
smtp_password = ''

[mysql]
dbhost = localhost
dbport = 3306
dbuser =
dbpass =
dbname =
table_prefix = 'ts_'
dbdebug  = false

[mongodb]
dbhost = localhost
dbport = 27017
dbuser = ''
dbpass = ''
dbname =
dbdebug  = true

[fb]
api_key = ''
api_secret = ''

[google]
maps_api = ''

[dotpay]
url = 'https://ssl.dotpay.pl/test_payment/'
id = ''
pin = ''
ip = ''
card_channel = 246
system_id = 40
api_version = 'dev'

[paybon]
system_id = 94

[payoffline]
system_id = 1

[paywallet]
system_id = 32

[p24]
url = 'https://sandbox.przelewy24.pl/api/v1'
panel_url = 'https://sandbox.przelewy24.pl'
server_ips = '**************|**************|**************|**************|**************'
merchant_id = 111111
pos_id ='11111'
secret_id = ''
url_return = ''
url_status = 'http://pay.epass.x-soft.it?payment&a=notify&type=p24'
api_key = ''
crc_code = ''
system_id = 182


[additionals]
qr_coupon_short_time = 'PT3M'
economic_card_name = 'default_img_new.jpg'
process_order = 'sync'
master_pass = ''
delay_emails = 1
delay_emails_period = 'PT10M'
session_in_get = 'Apple'
; session_in_get = 'Windows'
; session_in_get = 'Linux'
; session_in_get = 'Apple'
; session_in_get = 'Android'
; session_in_get = 'Safari'
; session_in_get = 'Chrome'
; session_in_get = 'Firefox'
; session_in_get = 'Edge'
qr_promo_use_hash = 'true'