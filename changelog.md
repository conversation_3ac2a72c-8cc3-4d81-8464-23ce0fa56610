# Changelog

All notable changes to this project will be documented in this file.

The format based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)



## [unreleased]
### Added
### Changed
### Fixed
### Removed


## [5.6.0] 2025-08-06
### Added
- mass topup for partenrs 

## [5.5.0] 2025-07-31
### Added
- HTML editor in all products

## [5.4.4] 2025-07-29
### Added
- HTML editor in tickets panel
### Changed
- Autoinvoice shwitch for partner. WHen deselcted invoices are not issued automatically
### Removed
- remove afterpay 'download' button


## [5.4.1] 2025-07-22
### Added
- generation timestamp on voucher PDF (regular template only)


## [5.4.0] 2025-07-22
### Added
- create seperate single files (js, css) for omnibus functionality
### Changed
- add wariant cenowy basic popup info about omnibus
- add omnibus crossed out price for ticket variants in template and basic style
### Fixed
- fix saveTicket method that was preventing 'zapisz' nowy karnet
### Removed
- check by user_id logic from GetCardFromMainPool() to allow topups from another accounts on partners,
- remove any reference to the prop $user_id in relevant method calls
### Tasks
- base-price-presentation-with-omnibus
- bank-account-number-validation:
- fix-new-offer-issue
- wp-validation-turn-off
- omnibus-new-product


## [5.3.14] 2025-07-15
### Fixed
- Multibilet - remove wrong attached code


## [5.3.13] 2025-06-26
### Added
- removeOldBaskets command

## [5.3.12] 2025-06-26
### Fixed
- limit in getOldBaskets, old basket removed right after processing
- fixed basket remove procedure, extra code removed when order is paid, cancel order - implemented extra code reservation release


## [5.3.11] 2025-06-24
### Added
- Multibilet - extra code imprinted in PDF for Planetarium feature. Coupon version

## [5.3.10] 2025-06-24
### Tasks
- fix-raport-zero
- add-attractions-in-the-partner-panel
- fix downloading sales report

## [5.3.9] 2025-06-09
### Tasks
- task: add attraction row filter

## [5.3.8] 2025-06-09
### Tasks
- task: removing-the-Passes-tab---Excel-report


## [5.3.6] 2025-06-09
### Tasks
- task: topup-search

## [5.3.5] 2025-06-09
### Tasks
- task: elka-form-raport-xls
- task: elka-raport


## [5.3.4] 2025-06-04
### Tasks
- task: scroll-for-ticket-info
- task: ticket-pool-reset

## [5.3.3] 2025-06-04
### Fixed
- Correct voucher return value display in report, rolled back to previous version of the table (`application/admin_v4_0/modules/finance/reportsOsrodek.php`, `application/admin_v4_0/views/modules/finance/finance-scripts-osrodek.tpl`, `application/admin_v4_0/views/modules/finance/finanse_raport_osrodek.tpl`)

## [5.3.2] 2025-06-03
### Fixed
- Apple safari display

## [5.3.0] 2025-05-22
### added
- Multibilet

## [5.2.9] 2025-04-23
### fix
- fix voucher description

## [5.2.8] 2025-04-23
### fix
- omnibus fix on voucher

## [5.2.7] 2025-04-08
### task
- task: scroll-for-ticket-info

## [5.2.6] 2025-03-26
### task
- task: zakres-czasowy-od

## [5.2.5] 2025-03-26
### task
- task: raport-osrodek-grouping

## [5.2.4] 2025-03-26
### task
- task: summary-tables

## [5.2.3] 2025-03-13

### task

- no-login-banner

## [5.2.2] 2025-03-13

### task

- after-party-banner

## [5.2.1] 2025-03-13

### task

- history-search-fix

## [5.2.0] 2025-03-13

### task

- admin-nav

## [5.1.6] 2025-02-10

### Added

- Front - card number validator

## [5.1.4] 2025-02-07

### Fixed

- repeatPayment now works with partner payment system

## [5.1.3] 2025-02-05

### Fixed

- repeatPayment now works with no-login flow

## [5.1.0] 2025-01-22

### Fixed

- partnerVoucherToZip
- topupFix
- wispassCardProductionFix
- removing info about duration
- fixKupony - we need another fix

## [5.0.17] 2025-01-17

### Fixed

- insurance callendar minimum date

## [5.0.16] 2025-01-09

### Added

- delete Ticket from voucher (pdf card)

## [5.0.14] 2024-12-09

### Fix

- use static icons instead of cdn
- use a diferent tooltip tippy provider to improve perfomance, refactor tippy configuration in ticks config and option error
- send email after successful admin manual card top up operation
- implement osrodek history selection in 'finanse osrodek'
- display search history for finanse sprazedaz tab
- set quantity for karnet voucher in partner
- add ios js touch event handler
- add validation to the html containing fields in edit\_
- trim i login
- move zip downloading to the top

## [5.0.10] 2024-11-20

### Fix

- remove animations from configuration
- fix each ticket prices
- fix the price for the prices more than 1000
- remove additional shop ticket price html, the price is set in scripts for tickets
- fix date for 'oferta wazna ' text
- remove payment and configure animations

## [5.0.2] 2024-11-04

### Fix

- client payment

## [5.0.1] 2024-10-24

### Fix

- payment (DP)

## [5.0.0] 2024-10-23

### Changed

- grafika sklepu
- zmiany we flow zakupów
- inne

## [4.11.1] 2024-09-12

### Changed

- autoakcepotacja po 5 dniach
- faktury ujemne nie są generowane
- automatyczna wysyłka faktury w meilu (samofakturowanie)

## [4.11.0] 2024-03-13

### Changed

- samofakturowanie - liczenie od brutto
- raport księgowej: podsumy tylko btrutto

## [4.10.0] 2024-01-25

### Added

- Fakturownia

## [4.9.0] 2024-01-18

### Added

- pre fakturowni - formularze na dane

### Changed

- promo hash. włączanie w config.ini

## [4.8.3] 2024-01-10

### Fixed

- cookie in header when ajax request is sent
- sprzedaz report - midnight starts at 00:00 now

## [4.8.0] 2023-12-05

### Added

- Newsletter Raport

### Fixed

- raport ubezpieczenia, paginacja faktur partner

### Changed

- usunięcie zamawiania faktury papierowej

## [4.7.4] 2023-11-24

### Changed

- nie wysylam meili po utworzeniu nowego partnera
- mailing list - ukrywanie wiadomosci, ponowna wysyłka dla wiadomości które miały mniej niż 6 prób, ukrywanie wiadomości z wiekszą niż 5 liczbą prób wysyłki

## [4.7.3] 2023-11-24

### Changed

- Ukrycie ponowienia płatności na koncie klienta gdy zamówienie jest opłacane przez bramkę partnera

## [4.7.0] 2023-11-20

### Added

- Newsletter checkbox przy zamówieniach

## [4.4.0] 2023-09-16

### Fixed

- sprawedzanie kuponu uwzględnia wielkość liter, koszt transportu jeżeli user jest niezalogowany

### Added

- Kupon ZIEMNIAK moze byc dodany do zamowienia wiele razy, ale do produktu tylko raz

## [4.3.3] 2023-09-06

### Fixed

- pobieranie pdf w ZIP - fix

### Changed

- wysyłka faktury w meilu
- dodanie obrazków na karta-pdf

### Added

- derfaultowe zaznaczanie nośnika karty
- meil z WP po zakupie karnetu
- szukajka partner vouchery

## [4.3.0] 2023-08-24

### Changed

- dodanie transportu do raportów

### Added

- Apple Safari w ramce
- pobieranie pdf w ZIP

## [4.2.0] 2023-05-31

### Changed

- small fixes

### Added

- nazwany import kart

## [4.1.0] 2023-03-14

### Changed

- uruchomione zakupy bez logowania

### Added

## [4.0.2] 2023-02-09

### Changed

- Historia panel partnera
- poprawka do download PDF
- status vouchera - usunieto
- panel klienta, usuniecie statusu vouchera/karnetu
- wyłączanie maili po akceptacji rozliczenia
- dane uzytkownika do karty_prod pobierane z mongo -> adres_dostaw

## [4.0.0] 2023-01-25

### Changed

- skrócony proces zakupowy

### Added

- zakupy bez logowania
- dyrektywa omnibus

## [3.23.0] 2022-12-06

### Changed

- rozliczenia dużych plików do gridFS

## [3.22.0] 2022-12-06

### Changed

- zwiększenie RAM dla generowania raportów księgowa, panel faktur obcych

## [3.21.7] 2022-11-25

### Changed

- usunięcie kolumny "ośrodki" na podglądzie u klienta

## [3.21.5] 2022-11-04

### Changed

- odkrycie zakadki vouchery

## [3.21.3] 2022-10-25

### Changed

- możliwość zakupu ubezp na ten sam dzień
- kolejność wyświetlania kart do produkcji

### Added

- Kupony wielokrotnego uzytku

## [3.21.0] 2022-08-18

### Changed

- zmiany w widokach Partnerów i klientów
- aktualizacje i poprawki w adminie (popup detale)

## [3.20.0] 2022-08-04

### Changed

- ukryte w sprzedaży vouchery
- karnety posiadają nośnik, który może być kartą lub plikiem PDF

### Added

- zmiany w panelach

## [3.19.0] 2022-06-29

### Changed

- zmiana ilości wyświetlanych produktów na panelu usera
- możliwość wyłączeniea wyświetlania dat ważności oferty na froncie
- zmiana w raporie partnera

### Added

- mechanizm sprawniejszej aktualizacji numerów kart i przesyłki

## [3.18.0] 2022-04-26

### Changed

- zmiana wyświetlania listy ubezpieczeń
- zmiana wyświetlania i raportu dla ośrodka

## [3.17.7] 2022-03-25

### Added

### Changed

- Zmiana tabeli karnety w panelu partnera sprzedażowego

## [3.17.4] 2022-01-31

### Added

- numery zamówien na liście karnetów
- Szukajka VP na panelu admina

### Changed

## [3.17.3] 2022-01-25

### Added

- komenda resetowania tabeli exchange_rej

### Changed

## [3.17.0] 2022-01-20

### Added

### Changed

- Zmiana Raportu ubezpieczeń - dodatkowe kolumny status, stawka, filtry w panelu ubezpieczeń

## [3.16.0] 2022-01-18

### Added

- Raport dla partnera z własna bramką płatnosci

### Changed

## [3.15.0] 2022-01-11

### Added

- Email z potwierdzeniem płatności jest wysyłany z opóźnieniem
- Usunięcie numeru vouchera z PDFa vouchera,
- Generator kuponów dla użytkownika

### Changed

## [3.13.0] 2021-12-23

### Added

- panel do zwrpotów produktów przez partnera
- poprawka 'brak' na fakturach,
- pobieranie faktur przez księgową

### Changed

## [3.12.6] 2021-12-21

### Added

### Changed

- Raport księgowej: dodane dane faktury, odfiltrowane zamówienia z płatnością na klienta

## [3.12.0] 2021-12-16

### Added

- Panele meilingu

### Changed

## [3.11.0] 2021-12-16

### Added

- Kupony procentowe

### Changed

## [3.10.0] 2021-12-13

### Added

- Faktury obce
- Kasowanie karnetów i voucherów inicjowane z BS

### Changed

- Kasowanie karnetów i voucherów inicjowane z ESKI

## [3.9.7] 2021-12-13

### Added

- Komenda do ponownej wysyłki eili z 27 listopada 2021

### Changed

## [3.9.6] 2021-12-08

### Added

- Kasowanie karnetów i voucherów po zwrotach
- Migracja do indeksu unique do echange_tid

### Changed

## [3.9.5] 2021-12-06

### Added

- Dobicia voucherów

### Changed

## [3.9.2] 2021-12-02

### Added

- Informacja o fakturze do wysyłki w panelu produkcji karty

### Changed

## [3.9.1] 2021-12-02

### Added

### Changed

- Wyłączenie generowania PDF przy rozliczeniach
- Produkty sprzedawane przez WP są zawsze procesowane 'sync'

## [3.9.0] 2021-12-01

### Added

- Dobicia kart

### Changed

## [3.8.2] 2021-11-27

### Added

### Changed

- Konfiguracja mailingu do obsługi ssl i tls

## [3.8.1] 2021-11-26

### Added

### Changed

- BUGFIX do wyświetlania rabatów na stronie sklepu

## [3.8.0] 2021-11-25

### Added

- zamówienie moze być procesowane synchronicznie lub asynchronicznie

### Changed

## [3.7.1] 2021-11-25

### Added

- zakładka karnety do panelu partera osrodka

### Changed

## [3.7.0] 2021-11-22

### Added

- nowy wizerunek karty, tekst o zwrotach

### Changed

- wyswietlanie rabatu przy voucherach

## [3.6.1] 2021-11-19

### Added

- kasowanie biletów

### Changed

## [3.6.0] 2021-11-19

### Added

### Changed

- numery vouchera w meilu
