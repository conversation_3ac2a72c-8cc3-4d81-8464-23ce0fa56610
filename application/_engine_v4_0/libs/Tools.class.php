<?php

/**
 * Klasa narzędziowa wspomagająca wiele modułów <br>
 * @usage activate() Aktywacja przez klasę activate()
 * @package Application
 * @subpackage Libs
 * @uses Tools::activate();
 */
class Tools {

    private static $baza = null;
    public static $urlparams = '';
    private static $active = false;
    public static $obroty = false;
    public static $dump = array();
    public static $moduleerror = array();

    public static $errors = [];

    /**
     * Klasa aktywująca
     * @param type $vars zmienne $_GET
     */
    public static function activate($vars = null) {
        if(!self::$active){
        self::$baza = Database::activate();
        self::$urlparams = ($vars === null?$_GET:$vars);
        self::$active = true;
        }
    }


    /**
     * Zwraca tablicę z ID obiektów usera
     * @param int $userid ID Usera
     * @param string $table Nazwa tabeli (typ obiektu)
     * @return array
     */
    public static function GetUserObjects($userid, $table = 'accommodation') {
        if(!self::$active) self::activate();
        $tblname = self::$baza->Escape($table);
        $r = self::$baza->get_filtered_rows($tblname, 'id', array(array('id_user', $userid, 'INT')));
        if (self::$baza->num_rows() > 0) {
            foreach ($r as $v) {
                $o[] = $v['id'];
            }
        }
        else
            $o = array();
        return $o;
    }
    

    /**
     * Pobiera listę możliwych statusów ofert
     * @param string $table Nazwa tabeli z listą obiektów
     * @return array Tablica get_filtered_row
     */
    public static function GetOfferStatuses($table = 'offer_status') {
        if(!self::$active) self::activate();
        $tblname = $table;
        $fields = '*';
        return self::$baza->get_filtered_rows($tblname, $fields, false, false, false, false);
    }

    /**
     * Pobiera listę partnerów wg typu lub wszystkie
     * @param string $typ ID typu partnera
     * @param bool $sortby Jeżeli TRUE zwrócona tablica jest sortowana po ID partnerów. Ustawione na FALSE
     * @param bool $onlyActive Jeżeli TRUE wybierz tylko aktywnych. Default FALSE.
     * @return array Tablica get_filtered_row
     */
    public static function GetPartners($typ = 0, $sortby = false, $onlyActive = false) {
        if(!self::$active)
        {
            self::activate();
        }
        $tblname = 'partners';
        $fields = '*';
        if($onlyActive)
        {
            $where = [['active', 1, 'INT']];
        } else {
            $where = false;
        }
        if($typ!=0){
            if(is_array($typ)){
                $rows = self::$baza->get_rows_where_in($tblname, 'partnertype', $typ);
            }
            else{
                if(!$where)
                {
                    $where = array(array('partnertype',$typ,'INT'));
                } else {
                    $where[] = ['partnertype',$typ,'INT'];
                }
                $rows = self::$baza->get_filtered_rows($tblname, $fields, $where, 'name', false, false);
            }
        } 
        else {
        $rows = self::$baza->get_filtered_rows($tblname, $fields, $where, 'name', false, false);
        }
        if(false === is_array($rows) OR false === $rows) {
            $rows = array();
        }
        if($sortby){
            foreach($rows as $v){
                $ret[$v['id']] = $v;
            }
            unset($rows);
            $rows = $ret;
        }
        return $rows;
    }
    
    /**
     * pobiera listę typów partnerów
     * @return array
     */
    public static function GetPartnerType($sortbyid = false) {
        if(!self::$active) self::activate();
        if($sortbyid){
            $sql = "SELECT * FROM " . TABLE_PREFIX . "partner_type ORDER BY name ASC";
            self::$baza->query($sql);
            while($row = self::$baza->fetch_row()){
                $dane[$row['id']] = $row;
            }
        }
        else{
        $dane = self::$baza->select("SELECT * FROM " . TABLE_PREFIX . "partner_type ORDER BY name ASC");
        }
        return $dane;
    }
    
    /**
     * Pobiera dane partnera
     * @param int $pid ID partnera
     * @return boolean FALSE if false or
     * @return array Tablica z danymi partnera 
     */
    public static function GetPartnerData($pid){
        if(!self::$active) self::activate();
        $ret = self::$baza->get_row('partners',$pid);
        if(false === $ret) return false;
        $ret['gpslat']=  Additional::GPSFromDB($ret['gpslat']);
        $ret['gpslon']=  Additional::GPSFromDB($ret['gpslon']);
        return $ret;               
    }

    /**
     * Pobiera dane WP partnera
     * @param int $pid ID partnera
     * @return boolean FALSE if false or
     * @return array Tablica z danymi partnera
     */
    public static function GetPartnerWPData($pid){
        if(!self::$active) self::activate();
        $ret = self::$baza->get_filtered_rows('wpconfig','*', [ ['osrodek', $pid, 'INT']]);
        if(false === $ret) return false;
        return $ret;
    }
    
    /**
     * Pobiera metadane partnera
     * @param int $idpartner ID partnera
     * @return boolean|array
     * @return array
     */
    public static function GetPartnerMetadata($idpartner){
        if(!self::$active) self::activate();
        $where[]= array('id_parent',$idpartner,'INT');
        $where[]= array('current',1,'INT');
        $rows = self::$baza->get_filtered_rows('partners_meta','*',$where);
        if(false===$rows) return false;
        if(!is_array($rows)) $rows= array();
        $meta = [];
        foreach($rows as $k=>$v){
            $meta[$v['properties']] = $v['value']; 
        }
        return $meta;
    }
    
    /**
     * Pobiera metadane partnerów danego typu
     * @param int $typ ID typu partnera
     * @return boolean
     * @return array
     */
    public static function GetPartnersMetadata($typ){
        if(!self::$active) self::activate();
        $where[]= array('typ_meta',$typ,'INT');
        $where[]= array('current',1,'INT');
        $rows = self::$baza->get_filtered_rows('partners_meta','*',$where);
        if(false===$rows) return false;
        if(!is_array($rows)) $rows= array();
        foreach($rows as $k=>$v){
            $meta[$v['id_parent']][$v['properties']] = $v['value']; 
        }
        return $meta;
    }

    /**
     * Pobiera rekord z danymi pojedynczego produktu
     * @param int $pid ID produktu
     * @return boolean FALSE
     * @return array Rekord z danymi
     */
    public static function GetPartnerProductData($pid){
        if(!self::$active) self::activate();
        $ret = self::$baza->get_row('partners_products',$pid);
        if(false === $ret) return false;
        return $ret;               
    }
    
    public static function GetPartnerProductDataHash($hash){
        if(!self::$active) self::activate();
//        $ret = self::$baza->get_row('partners_products',$hash, 'hash');
        $r = self::$baza->select_r('SELECT * FROM '.TABLE_PREFIX.'partners_products WHERE current = 1 AND hash = "'.$hash.'"');
        foreach($r as $k=>$v){
            $ret[$k] = $v;
        }
        if(false === $ret) return false;
        return $ret;               
    }
    
    /**
     * Pobiera meta dla danego produktu
     * @param type $pid ID produktu
     * @return boolean
     * @return array
     */
    public static function GetPartnerProductMetadata($pid){
        if(!self::$active) self::activate();
        $where[]= array('id_parent',$pid,'INT');
        $rows = self::$baza->get_filtered_rows('partners_products_meta','*',$where);
        if(false===$rows) return false;
        if(!is_array($rows)) $rows= array();
        foreach($rows as $k=>$v){
            $meta[$v['properties']] = $v['value']; 
        }
        return $meta;
    }
    /**
     * Pobiera języki dla danego produktu
     * @param type $pid ID produktu
     * @return boolean
     * @return array
     */
    public static function GetPartnerProductLangs($pid, $pidtype = 'parentid'){
        if(!self::$active) self::activate();
        switch($pidtype){
            case 'parentid':
            $where[]= array('parentid',$pid,'INT');
            $rows = self::$baza->get_filtered_rows('partners_products_langs','*',$where);
            if(false===$rows) return false;
            if(!is_array($rows)) $rows= array();
            break;
            case 'hash':
            $sql = 'SELECT ppl.property, ppl.langid, ppl.value FROM '.TABLE_PREFIX.'partners_products pp, '.TABLE_PREFIX.'partners_products_langs ppl '
                    . 'WHERE pp.hash = "'.$pid.'" AND pp.current=1 AND ppl.parentid = pp.id';
            $rows = self::$baza->select($sql);
            break;
        }
        foreach($rows as $k=>$v){
            $meta[$v['property']][$v['langid']] = $v['value']; 
        }
        return $meta;
    }
    
    /**
     * Pobiera wszystkie produkty danego partnera
     * @param int $partnerid ID partnera
     * @param bool $meta Jeżeli true w polu 'metadata' będą informacje z tabeli meta
     * @return boolean false
     * @return array Tablica produktów
     */
    public static function GetPartnerAllProducts($partnerid,$meta = false){
        if(!self::$active) self::activate();
        $pid = intval($partnerid);
        if($pid===0)return false;
        $where[] = array('parent_id',$pid,'INT');
        $where[] = array('current',1,'INT');
        $rows = self::$baza->get_filtered_rows('partners_products','*',$where);
        if(false===$rows) return false;
        if(!is_array($rows)) $rows = array();
        if($meta){
            foreach($rows as $k=>$v){
                $rows[$k]['metadata'] = self::GetPartnerProductMetadata($v['id']);
                $rows[$k]['langs'] = self::GetPartnerProductLangs($v['id']);
            }
        }        
        return $rows;         
    }
    
    /**
     * Pobiera listę wszystkich produktów partnerów danego typu (np: wszystkie produkty kurierów)
     * @param int $partnertype ID typu partnera
     * @param bool $meta Jeżeli true w polu 'metadata' będą informacje z tabeli meta
     * @return boolean FALSE
     * @return array
     */
    public static function GetPartnerTypeProducts($partnertype, $meta = false){
        if(!self::$active) self::activate();
        $ptid = intval($partnertype);
        if($ptid===0) return false;
        $where[] = array('partner_type_id',$ptid,'INT');
        $where[] = array('current',1,'INT');
        $rows = self::$baza->get_filtered_rows('partners_products','*',$where);
        if($rows===false) return false;
        if(!is_array($rows))$rows = array();
        if($meta){
            foreach($rows as $k=>$v){
                $rows[$k]['metadata'] = self::GetPartnerProductMetadata($v['id']);
            }
        }        
        return $rows;         
    }
    
    /**
     * Konwertuje jedną walutę na drugą
     * @param float $amount Wartość do przeliczenia
     * @return float $amount Wartość przeliczona
     */
    public static function ConvertCurrency($amount, $currcode = false) {
        if ($currcode) {
            if ($_SESSION['CURRENCYCODESIDE'] == 'left') {
                return $_SESSION['CURRENCYCODE'] . ' ' . number_format($amount * $_SESSION['CURRENCYRATE'], 2);
            }
            if ($_SESSION['CURRENCYCODESIDE'] == 'right') {
                return number_format($amount * $_SESSION['CURRENCYRATE'], 2) . ' ' . $_SESSION['CURRENCYCODE'];
            }
        }
        return number_format($amount * $_SESSION['CURRENCYRATE'], 2, '.', '');
    }
    
    public static function CC($amount, $currcode = false){
        return self::ConvertCurrency($amount, $currcode);
    }

    
     /**
     * Zapis pliku
     * @param array $files Tablica z plikami
     * @param int $filepath ID obiektu głównego / data dodania
     * @param string $table Nazwa tabeli (katalogu)
     * @param string $treetype Sposób tworzenia katalogów: 'id', 'date' (katalogu)
     * @return boolean FALSE Jeżeli błąd
     * @return string Jeżeli ok - ścieżka zapisu i nazwa pliku
     */
    public static function _saveImage($files, $filepath, $table, $treetype = 'id') {
        $dirtab = Additional::GetDir($filepath, 'array',$treetype);
        $dirtabtxt = Additional::GetDir($filepath, 'text',$treetype);
        $diradd = "";
//        $dirtab_t = array_reverse($dirtab);
//        array_push($dirtab_t, $table);
//        $dirtab = array_reverse($dirtab_t);
        array_unshift($dirtab,$table);
        foreach ($dirtab as $d) {
            if (!file_exists("data/" . $diradd . $d))
                mkdir("data/" . $diradd . $d);
            $diradd.=$d . "/";
        }
        $ext = Additional::FileExt($files['name']);
        $name = md5($files['name'] . time()) . '.' . $ext;
        if (!move_uploaded_file($files['tmp_name'], "data/" . $table . "/" . $dirtabtxt . "/" . $name)) {
            self::$dump[] = var_export($_FILES,true);
            self::$moduleerror[] = "data/" . $table . "/" . $dirtabtxt . "/" . $name;
            return false;
        }
        return "data/" . $table . "/" . $dirtabtxt . "/" . $name;
    }
    
    
    /**
     * Usuwa z dysku obraz i jego mutacje
     * @param string $path Ścieżka do pliku z nawą pliku
     * @param string $prefix prefiks do polecenia GLOB np '*'
     * @param string $suffix suffix do polecenia GLOB np '*'
     * @return boolean
     */
    public static function _deleteImages($path, $prefix = '', $suffix = '') {
        if ($path == DEFAULTIMAGE)
            return;
        $pparts = pathinfo($path);
        $ext = array('jpg', 'JPG', 'png', 'PNG');
        if (!in_array($pparts['extension'], $ext))
            return false;
        $path = $pparts['dirname'] . '/' . $prefix . $pparts['filename'] . $suffix . '.' . $pparts['extension'];
        $files = glob($path);
        foreach ($files as $file) {
            unlink($file);
        }
    }

    /**
     * Generowanie opcji '&lt;option&gt;' z godzinami
     * @param string $selected Która godzina ma być zaznaczona
     * @param type $f Generowanie godzin w trybie 12 godzinnym (nie działa)
     * @return type
     */
    public static function GetHours($selected = '-', $f = 24) {
        $out = '';
        switch ($f) {
            case 24:
                for ($i = 1; $i < 24; $i++) {
                    $selected == $i ? $zaz = 'selected' : $zaz = '';
                    $out.="<option value='" . str_pad($i, 2, '0', 0) . "' $zaz>" . str_pad($i, 2, '0', 0) . "</option>\n";
                }
                break;
            case 12:
                break;
        }

        return "<option value='--'>--</option>\n" . $out;
    }

    /**
     * Generowanie opcji '&lt;option&gt;' z minutami z możliwością określenia kroku
     * @param string/int $selected Która minuta ma być zaznaczona
     * @param int $step Co ile minut ma się pojawiać opcja
     * @return type
     */
    public static function GetMinutes($selected = '-', $step = 15) {
        $out = '';
        for ($i = 0; $i < 59; $i = $i + $step) {
            $selected == $i ? $zaz = 'selected' : $zaz = '';
            $out.="<option value='" . str_pad($i, 2, '0', 0) . "' $zaz>" . str_pad($i, 2, '0', 0) . "</option>\n";
        }
        return "<option value='--'>--</option>\n" . $out;
    }

    /**
     * Generowanie kalendarza z checkboxami do zakupu reklamy
     * @param string/int $month miesiąc MM 
     * @param string/int $year rok YYYY
     * @param float $price Cena reklamy na dany miesiąc
     * @param array $disabed dni wyłączone z miesiąca (disabled)
     * @return string
     */
    public static function GetCalendar($month = '01', $year = '2014', $price, $disabled = array()) {
        $data = strtotime($year . '-' . $month . '-01');
        if ($year . '-' . $month == date('Y-m')) {
            $today = date('d');
            $tmd = range(1, $today);
            $disabled = array_merge($disabled, $tmd);
        }
        # numer 1 dnia miesiąca w tygodniu 
        $first_dofw = date('N', $data);
        # ilość dni w miesiącu
        $days = date('t', $data);
        $go = true;
        # dień w tygodniu
        $day = 1;
        $l = 1;
        # dzień miesiąca
        $dayn = 1;
        $out = "<table class='table-calendar banner mt-10'><thead><tr>"
                . "<td colspan=7 style='text-align:center;'>" . date('m/Y', $data) . " - select all <input type='checkbox' id='sall" . $month . "' class='selectall' data-month=" . $month . "></td></tr><tr>"
                . "<td>Mon</td><td>Tue</td><td>Wed</td><td>Thu</td><td>Fri</td><td>Sat</td><td>Sun</td></tr></thead>";
        while ($go) {
            if ($l < $first_dofw) {
                $out.="<td class='cell empty'></td>";
                $l++;
                $day++;
                continue;
            }
            if (in_array($dayn, $disabled)) {
                $out.="<td class='cell red'>" . $dayn . "</td>";
            }
            else {
                $out.="<td class='cell free'>" . $dayn . "<br>"
                        . "<input type='checkbox' name='mth_" . $month . "[]' value='" . $dayn . "' data-cost='$price' class='calendarday'></td>";
            }
            $l++;
            if ($day == 7) {
                $out.="</tr><tr>";
                $day = 1;
            }
            else
                $day++;
            if ($dayn == $days)
                $go = false;
            $dayn++;
        }

        $out .= "</tr></table>";

        return $out;
    }

   
    /**
     * Zlicza ilość niemoderowany reviews
     * @return int
     */
    public static function ToModerate() {
        if(!self::$active) self::activate();
        $dane = self::$baza->select_r("SELECT count(id) as ile FROM " . TABLE_PREFIX . "reviews WHERE status=1");
        return intval($dane['ile']);
    }

    
    /**
     * Zlicza ilość niemoderowany reviews
     * @return int
     */
    public static function ToAccept() {
        if(!self::$active) self::activate();
        $iduser = USER::GetUserID();
        $sql = 'SELECT count(id) as ile FROM '.TABLE_PREFIX.'offer_accept_proccess WHERE status=-1 AND partnerid in (SELECT idpartner FROM '.TABLE_PREFIX.'partner_user WHERE iduser = '.$iduser.')';
        $dane = self::$baza->select_r($sql);
        return intval($dane['ile']);
    }
 
        
        /**
     * Dodaje lub aktualizuje tabelę typu '_meta' (activity_meta)
     * @pram string $table Nazwa tabeli bez przyrostka '_meta'
     * @param array $params Tablica w formacie[0]'value'=>'wartość'. Jeżeli w rekordzie będzie dodatkowo np: 'typmeta'=>'main_object'<br> ma on pierszeństwo przed parametrm funkcji
     * @param string $todo 'ADD' - dodaje nowy rekord, 'update' - aktualizuje pole 'value' rekordu po kluczu: id_object, typ_meta, properties
     * @return boolean
     */
    public static function _setMeta($table, $params, $todo = 'add',&$error = false) {
        if(!self::$active) self::activate();
        $tblname = $table . '_meta';
        foreach ($params as $k => $v) {
            $id_parent = (isset($params[$k]['id_parent']) ? $params[$k]['id_parent'] : false);
            $typ_meta = (isset($params[$k]['typmeta']) ? $params[$k]['typmeta'] : false);
            $properties = (isset($params[$k]['properties']) ? $params[$k]['properties'] : false);
            if(false === ($id_parent || $typ_meta || $properties)) return false;
            $data[] = array('id_parent', $id_parent, 'INT');
            $data[] = array('typ_meta', $typ_meta, 'STRING');
            $data[] = array('properties', $properties, 'STRING');           
            if ($todo == 'add') {
                $data[] = array('value', $v['value'], 'STRING');
                if (false == self::$baza->insert_row($tblname, $data)) {
                    if($error==true){
                        $error = 'Add meta error';
                    }
                    return false;
                }
            }
            if ($todo == 'update') {
                if(false == self::$baza->update_rows($tblname,array(array('value',$v['value'],'STRING')),$data)){
                    if($error==true){
                        $error = 'Update meta error';
                    }
                    return false;                    
                }
                
            }
            unset($data);
        }
        return true;
    }
    
     /**
     * Zapisuje meta dla danego partnera zachowując wersjowanie
     * @param int $idparent ID obiektu dla którego tworzymy opis
     * @param text $properties Właściwość obiekt dla którego tworzymy opis
     * @param text $textvalue Wartość opisu w danym języku
     * @return boolean
     */
    public static function _setPartnerMeta($idparent, $typmeta, $properties, $value, $lid = false) {
        if(!self::$active) self::activate();
        $properties = self::$baza->Escape($properties);
        $typmeta = self::$baza->Escape($typmeta);
        $textvalue = self::$baza->Escape($value);
        $sql_reset = 'UPDATE ts_partners_meta SET current=0 WHERE id_parent=' . $idparent . ' AND typ_meta="'. $typmeta .'" AND properties="'.$properties.'"';
        self::$baza->query($sql_reset);
        $sql = 'INSERT INTO ts_partners_meta SET id_parent='.$idparent .', typ_meta="'.$typmeta.'", properties="'.$properties.'", value="'.$textvalue.'"';
        if (self::$baza->query($sql)) {
            if ($lid) {
                self::$baza->query("SELECT last_insert_id()");
                $r = self::$baza->fetch_row('NUM');
                return $r[0];
            }
            return true;
        }
        return false;
    }

    /**
     * Zapisuje meta dla produktu partnera zachowując wersjowanie
     * @param int $idparent ID obiektu dla którego tworzymy opis
     * @param text $properties Właściwość obiekt dla którego tworzymy opis
     * @param text $textvalue Wartość opisu w danym języku
     * @return boolean
     */
    public static function _setPartnerProductMeta($idparent, $typmeta, $properties, $value, $lid = false) {
        if(!self::$active) self::activate();
        $properties = self::$baza->Escape($properties);
        $typmeta = self::$baza->Escape($typmeta);
        $textvalue = self::$baza->Escape($value);
        $meta_sql = 'SELECT ts_partners_products_meta WHERE id_parent=' . $idparent . ' AND typ_meta="'. $typmeta .'" AND properties="'.$properties.'"';
        $oldmeta = self::$baza->query($meta_sql);
        if(!empty($oldmeta)) {
            $sql = 'UPDATE ts_partners_products_meta SET value="'.$textvalue.'" WHERE id_parent='.$idparent .' AND typ_meta="'.$typmeta.'" AND properties="'.$properties.'"';
        } else {
            $sql = 'INSERT INTO ts_partners_products_meta SET id_parent='.$idparent .', typ_meta="'.$typmeta.'", properties="'.$properties.'", value="'.$textvalue.'"';
        }
        if (self::$baza->query($sql)) {
            if ($lid) {
                self::$baza->query("SELECT last_insert_id()");
                $r = self::$baza->fetch_row('NUM');
                return $r[0];
            }
            return true;
        }
        return false;
    }
    
    /**
     * Zapisuje produkt partnera zachowując wersjowanie
     * @param int $idparent ID obiektu dla którego tworzymy opis
     * @param text $subtype Typ produktu
     * @param text $name Nazwa produktu
     * @param text $ptype ID typu partnera
     * @param text $hash Wartość hasha
     * @return boolean
     * @return int
     */
    public static function _setPartnerProduct($idparent, $subtype, $name, $ptype, $hash, $lid = true)
    {

        $product = PartnerService::insertPartnerProduct($idparent, $subtype, $name, $ptype, $hash);

        if(false == $product) {
            return false;
        }

        if ($lid) {
           return $product;
        }

        return true;

    }
    
    /**
     * string $c is the string of characters to use. 
     * integer $l is how long you want the string to be. 
     * boolean $u is whether or not a character can appear beside itself. 
     * @param type $c
     * @param type $l
     * @param type $u
     * @return type
     */
    //TODO sprawdzić działanie tej metody !!!!
     public static function rand_chars($c, $l, $u = FALSE) {
        if (!$u)
            for ($s = '', $i = 0, $z = strlen($c) - 1; $i < $l; $x = mt_rand(0, $z), $s .= $c[$x], $i++)
                ;
        else
            for ($i = 0, $z = strlen($c) - 1, $s = $c[mt_rand(0, $z)], $i = 1; $i != $l; $x = mt_rand(0, $z), $s .= $c[$x], $s = ($s[$i] == $s[$i - 1] ? substr($s, 0, -1) : $s), $i = strlen($s))
                ;
        return $s;
    }
    /**
     * Tworzy numer seryjny kuponu
     * @return string
     */
    public static function GenCouponSerial($prefix=''){
        $cprefix=0;
        if($prefix!=''){
            $cprefix = strlen($prefix);
            if($cprefix>12){
                $prefix = substr($prefix,0,12);
                $cprefix = 12;
            }
        }
        $a = 'ABCDEFGHIJKLMNPQRSTUVWXYZ1234567890';
        $ct = microtime(true);
        list($msec, $sec) = explode('.',$ct);
        $n=  $prefix . self::rand_chars($a, 14-$cprefix);
        $n .= $sec.mt_rand(10,99);
        return join('-',str_split($n,4)); 
    }
    /**
     * Tworzy numer seryjny vouchera
     * @return string
     */
    public static function GenVoucherSerial(){
        $counter = 1;
        $ef = false;
        do{
        $d1 = str_pad(mt_rand(0, 9999),4,'0',STR_PAD_LEFT);
        $d2 = str_pad(mt_rand(0, 999),3,'0',STR_PAD_LEFT);
        $d3 = str_pad(mt_rand(0, 999),3,'0',STR_PAD_RIGHT);
        $da = $d1.$d2.$d3;
        $cs = str_pad(array_sum(str_split($da)),2,'1');
        $serial = $cs.$da;
        if(!self::$active) self::activate();
        $ef = self::$baza->query('INSERT INTO '.TABLE_PREFIX.'vouchers_serials SET serial = "'.$serial.'"');
        if(false === $ef){
            @file_put_contents ('vserial_collision.txt', date('Y-m-d H:i:s').' | serial: '.$serial.' | '.$counter++);
        }
        }while ($ef === false);
        return $serial; 
    }

    
    // WYŚWIETLANIE TABLICY
    public static function PA( $array, $title = false, $target = 'screen' )
    {
        $out = '<div style="padding: 5px; border-bottom: 1px dotted grey;">';
            if ( $title )
            {
                $out .= '<span style="font-family: helvetica, sans-serif; font-weight: 700; margin-bottom: 5px" >'.$title.'</span>';
            }
            
            $out .= '<pre STYLE="font-family:monospace; font-size:12px; word-wrap: break-word; ">';
            $out .= var_export($array,true);
            $out .= '</pre>';
        $out .= '</div>';
     if($target === 'screen'){
         echo $out;
     } else{
     @file_put_contents('_pa_.dat', $out, 8);
     }  
        
    }
    
    /**
     * Zwraca tablice z nazwami typów kart
     * @param type $lang
     * @return type
     */
    public static function GetCardTypes($lang=null){
        if(!self::$active) self::activate();
        if(is_null($lang)){
            $lang = LANG;
        }
        else $lang = intval($lang);       
        $types=self::$baza->get_filtered_rows('typy_kart',array('typ_id','value'),array(array('lang_id',$lang),array('active',1)));
        foreach($types as $type){
            $res[$type['typ_id']] = $type;
        }
        return $res;
    }
    
    private static function FillLangVersion($data){
        if(!is_array($data)) return $data;
        if('' == $first = current($data)){
            while ($first==''){
                $first = next($data);
            }
        }
        reset($data);
        foreach($data as $k=>$v){
            if($v=='') $data[$k] = $first.' - translate!';
        }
        return $data;
    }

    /**
     * Zwraca listę dat zużycia karnetu
     * @param int $id id karnetu
     * @return mixed
     */
    public static function GetPassUseList($id){
        if(!self::$active) self::activate();   
        $list=self::$baza->get_filtered_rows('karnety_zuzycie','id,data,id_bramki',array(array('id_karnetu',$id),array('zuzycie',0)));
        if(false!==$list && !is_null($list)){
            usort($list, function($a, $b) {
                return strtotime($a['data']) - strtotime($b['data']);
            });
        }
        return (!empty($list) ? $list : false);
    }

    /**
     * Sprawdza czy Email nie jest już używany w bazie użytkowników
     * @param string $email
     * @return mixed
     */
    public static function CheckIfEmailNotUsed($email){
        if(!self::$active) self::activate();     
        $check=self::$baza->get_field_val('uzytkownik', 'email', array(array('email', $email, 'STRING')));
        return (empty($check) ? true : false);
    }

    /**
     * Dodaje kartę do użytkownika (API)
     * @param type $serial
     * @param type $admin
     * @return boolean
     */
    public static function AddUserCard($serial, $admin=false){
        if(!self::$active) self::activate(); 
        if(false==$admin){
            $userid = Sessions::$user_id;
        }
        return true;
    }

    /**
     * Ustala obrót na dany dzień
     * @param mixed $group
     * @return boolean
     */
    public static function AgregateIncome($group = 'all'){
        if(!self::$active) self::activate(); 
        if($group=='all'){
            $sql = "SELECT sum(brutto)/100 as obrot, grupa_obrotowa FROM ts_zamowienia_przychod WHERE data_platnosci > 0 GROUP BY grupa_obrotowa";
            self::$baza->query($sql);
            while($row = self::$baza->fetch_row()){
                if($row['grupa_obrotowa']==0) continue;
                $obroty[$row['grupa_obrotowa']]=$row['obrot'];
            }
            if(count($obroty)>0){
            foreach($obroty as $k=>$v){
                $sql = 'UPDATE '.TABLE_PREFIX.'prowizja_grupy SET wartosc='.$v.' WHERE id='.$k;
                self::$baza->query($sql);
            }
            
            return $obroty;
            }
        }
        return true;
    }
    
    /**
     * Ustala prowizje na dany moment analizując obroty (karnety/vouchery)
     * @param array $offer
     */
    public static function FindProvision($offer,&$oldprow){
        if(!self::$active) self::activate();
        if(false === self::$obroty){
        $ob = self::$baza->get_filtered_rows('prowizja_grupy',array('id','wartosc'),array(array('status',1,'INT')));
        foreach($ob as $k=>$v){
            self::$obroty[$v['id']] = $v['wartosc']; 
        }
        unset($ob);
        }
        if($offer['karnety']){
            foreach($offer['karnety'] as $k=>$karnet){
//                echo '<br>Karnet: <br>';
                $grupa = intval($karnet['prowizja'][0]['grupa']);
                if($grupa == 0){
                    $oldp = intval($oldprow[$karnet['identyfikator']]['prowizja']);
                    $curp = intval($karnet['prowizja'][0]['wartosc']*10000);
                    if($oldp!==$curp){
                        $sql_u='UPDATE '.TABLE_PREFIX.'prowizja_aktualna SET current=0 WHERE id_produktu="'.$karnet['identyfikator'].'"';
                        self::$baza->query($sql_u);
                        $sql = 'INSERT INTO '.TABLE_PREFIX.'prowizja_aktualna SET id_produktu="'.$karnet['identyfikator'].'", prowizja='.$karnet['prowizja'][0]['wartosc']*10000;
                        self::$baza->query($sql);
                    }
                }
                else {
                    $obroty = self::$obroty[$grupa];
                    $prog = count($karnet['prowizja'])-1;
                    foreach($karnet['prowizja'] as $kk=>$prow){
                        if($obroty>$prow['prog']*1000+1) continue;
                        $prog = ($kk==0?0:$kk-1);
                        break;
                    }
                    $oldp = intval($oldprow[$karnet['identyfikator']]['prowizja']);
                    $curp = intval($karnet['prowizja'][$prog]['wartosc']*10000);
                    if($oldp!==$curp){
                        $sql_u='UPDATE '.TABLE_PREFIX.'prowizja_aktualna SET current=0 WHERE id_produktu="'.$karnet['identyfikator'].'"';
                        self::$baza->query($sql_u);
                        $sql = 'INSERT INTO '.TABLE_PREFIX.'prowizja_aktualna SET id_produktu="'.$karnet['identyfikator'].'", prowizja='.$karnet['prowizja'][$prog]['wartosc']*10000;
                        self::$baza->query($sql);
                    }else{
                    }
                }
            }
        }          
        if($offer['vouchery']){
            foreach($offer['vouchery'] as $k=>$voucher){
                $grupa = intval($voucher['prowizja'][0]['grupa']);
                if($grupa === 0){
                    $oldp = intval($oldprow[$voucher['identyfikator']]['prowizja']);
                    $curp = intval($voucher['prowizja'][0]['wartosc']*10000);
                    if($oldp!==$curp){
                        $sql_u='UPDATE '.TABLE_PREFIX.'prowizja_aktualna SET current=0 WHERE id_produktu="'.$voucher['identyfikator'].'"';
                        self::$baza->query($sql_u);
                        $sql = 'INSERT INTO '.TABLE_PREFIX.'prowizja_aktualna SET id_produktu="'.$voucher['identyfikator'].'", prowizja='.$voucher['prowizja'][0]['wartosc']*10000;
                        self::$baza->query($sql);
                    }
                }
                else {
                    $obroty = floatval(self::$obroty[$grupa]);
                    $prog = count($voucher['prowizja'])-1;
//                    echo 'Obroty: '.$obroty.'<br>';
                    foreach($voucher['prowizja'] as $kk=>$prow){
                        if($obroty>$prow['prog']*1000+1) {
                            continue;
                        }
                        $prog = ($kk==0?0:$kk-1);
                        break;
                    }
//                    echo 'Próg odnaleziony: '.$prog.'<br>Czy kwoty są równe?<br>';
                    $oldp = intval($oldprow[$voucher['identyfikator']]['prowizja']);
                    $curp = intval($voucher['prowizja'][$prog]['wartosc']*10000);
                    if($oldp !== $curp){
//                        echo 'Prowizja różna<br>';
//                        echo 'Oldprowizja: '.$oldp.'<br>';
//                        echo 'Prowizja progu: '.$curp.'<br>';
                        $sql_u='UPDATE '.TABLE_PREFIX.'prowizja_aktualna SET current=0 WHERE id_produktu="'.$voucher['identyfikator'].'"';
                        self::$baza->query($sql_u);
                        $sql = 'INSERT INTO '.TABLE_PREFIX.'prowizja_aktualna SET id_produktu="'.$voucher['identyfikator'].'", prowizja='.$voucher['prowizja'][$prog]['wartosc']*10000;
                        self::$baza->query($sql);
                    }else{
//                        echo 'brak różnic w prowizji<br>';
//                        echo 'Oldprowizja: '.$oldp.'<br>';
//                        echo 'Prowizja progu: '.$curp.'<br>';
                    }
                }
            }
        }          
    }
    
    /**
     * Przygotowuje cennik i historię dla produktu, zapisując WSZYSTKIE zdefiniowane okresy.
     * @param array $offer Oferta z mongo
     * @param string $date Data, dla której ustalić cennik. Jeżeli null - data aktualna
     */
    public static function PreparePricelists($offer, $date = null) {
        $product_types = ['karty', 'karnety', 'vouchery', 'ubezpieczenia', 'produkty_wirtualne'];
        
        foreach ($product_types as $type) {
            $product_type_singular = rtrim($type, 'y'); // karnety -> karnet
            if ($product_type_singular == 'kart') $product_type_singular = 'karta'; // Specjalny przypadek dla 'karty'
            if (substr($product_type_singular, -1) == 'e') $product_type_singular = substr($product_type_singular, 0, -1); // ubezpieczenia -> ubezpieczenie

            if (isset($offer[$type]) && is_array($offer[$type])) {
                foreach ($offer[$type] as $produkt) {
                    $wszystkie_ceny = self::_getAllProductPrices($produkt, $product_type_singular);

                    if ($wszystkie_ceny !== false) {
                        foreach ($wszystkie_ceny as $cena_info) {
                            self::priceHistory($offer['parentid'], $produkt['identyfikator'], $cena_info['wariant'], $cena_info['cena_od'], $cena_info['cena_do'], $cena_info['cena']);
                        }
                    }
                }
            }
        }
        
        $cdate = is_null($date) ? date('Y-m-d'): date('Y-m-d',$date);
        $sql_delete_current = 'DELETE FROM ' . TABLE_PREFIX . 'cennik WHERE id_oferty = "' . $offer['parentid'] . '"';
        self::$baza->query($sql_delete_current);

        $sql_get_current_prices = 'SELECT * FROM ' . TABLE_PREFIX . 'cenniki_history 
                                   WHERE id_oferty = "' . $offer['parentid'] . '"
                                     AND cena_od <= "' . $cdate . ' 23:59:59"
                                     AND cena_do >= "' . $cdate . ' 00:00:00"';
        
        $current_prices = self::$baza->select($sql_get_current_prices);

        if (!empty($current_prices)) {
            foreach ($current_prices as $cp) {
                $sql_insert_current = 'INSERT INTO ' . TABLE_PREFIX . 'cennik 
                                       SET id_oferty="' . $cp['id_oferty'] . '", 
                                           id_produktu="' . $cp['id_produktu'] . '", 
                                           cena=' . $cp['cena'] . ', 
                                           id_wariantu="' . $cp['id_wariantu'].'"';
                self::$baza->query($sql_insert_current);
            }
        }
    }

    /**
     * Przygotowuje historie dla produktu
     * @param array $offer Oferta z mongo
     * @param string $date Data, dla której ustalić cennik. Jeżeli null - data aktualna
     */
    public static function PreparePriceHistory($offer, $date = null) {
        // Ta funkcja jest teraz redundantna, ponieważ jej logikę przejęła PreparePricelists.
        // Dla bezpieczeństwa wywołujemy główną funkcję.
        self::PreparePricelists($offer, $date);
    }

    public static function priceHistory($offer, $product, $wariant, $data_od, $data_do, $cena)
    {
        $data_od_str = $data_od->format('Y-m-d H:i:s');
        $data_do_str = $data_do->format('Y-m-d H:i:s');

        $sql_check = 'SELECT id FROM '.TABLE_PREFIX.'cenniki_history 
                      WHERE id_oferty="'.$offer.'" 
                        AND id_produktu="'.$product.'" 
                        AND id_wariantu="'.$wariant.'"
                        AND cena_od = "'.$data_od_str.'"
                        AND cena_do = "'.$data_do_str.'"';
        
        $exists = self::$baza->select_r($sql_check);

        if (empty($exists)) {
            $cdate = date('Y-m-d');
            self::$baza->query('INSERT INTO ' . TABLE_PREFIX . 'cenniki_history 
                               SET id_oferty="' . $offer . '", 
                                   id_produktu="' . $product . '", 
                                   id_wariantu="' . $wariant.'", 
                                   cena=' . $cena . ', 
                                   data="'.$cdate.'", 
                                   cena_od="'. $data_od_str . '", 
                                   cena_do="'. $data_do_str .'"');
        }
    }

    public static function getLastLowestPrice($id_oferty, $id_produktu, $wariant, $base_price = null)
    {
        self::activate();

        if ($base_price === null) {
            return null;
        }

        $today = date('Y-m-d H:i:s');
        $bp = (float)$base_price;

        // KROK 1: Znajdź aktualnie trwającą promocję (najnowszą, która już się zaczęła)
        $sql_current_promo = 'SELECT cena, cena_od FROM ' . TABLE_PREFIX . 'cenniki_history 
                              WHERE id_oferty="' . $id_oferty . '" 
                                AND id_produktu="' . $id_produktu . '" 
                                AND id_wariantu="' . $wariant . '"
                                AND cena < ' . $bp . '
                                AND cena_od <= "' . $today . '" 
                                AND cena_do >= "' . $today . '"
                              ORDER BY cena_od DESC 
                              LIMIT 1';
        
        $currentPromo = self::$baza->select_r($sql_current_promo);

        // KROK 2: Jeśli nie ma dziś promocji, nie pokazujemy ceny Omnibus
        if (empty($currentPromo)) {
            return $base_price;
        }

        // KROK 3: Zdefiniuj "okno Omnibus" - 30 dni PRZED startem obecnej promocji
        $promoStartDate = $currentPromo['cena_od'];
        $omnibusWindowEnd = date('Y-m-d H:i:s', strtotime($promoStartDate . ' -1 second'));
        $omnibusWindowStart = date('Y-m-d H:i:s', strtotime($promoStartDate . ' -30 days'));

        // KROK 4: Znajdź najniższą cenę w tym precyzyjnym "oknie"
        $sql_omnibus_price = 'SELECT MIN(cena) as min_price FROM ' . TABLE_PREFIX . 'cenniki_history 
                               WHERE id_oferty="' . $id_oferty . '" 
                                 AND id_produktu="' . $id_produktu . '" 
                                 AND id_wariantu="' . $wariant . '"
                                 AND cena_od <= "' . $omnibusWindowEnd . '" 
                                 AND cena_do >= "' . $omnibusWindowStart . '"';

        $result = self::$baza->select_r($sql_omnibus_price);
        $lowestPriceInWindow = $result['min_price'];

        // KROK 5: Zwróć wynik
        if ($lowestPriceInWindow === null) {
            // Jeśli w oknie nie było żadnej ceny, odniesieniem jest cena bazowa
            return $base_price;
        } else {
            // W przeciwnym razie, zwracamy najniższą znalezioną cenę
            return (float)$lowestPriceInWindow;
        }
    }

    public static function RemovePricelists($offerid)
    {
        self::activate();
        
        $sql_delete_current = 'DELETE FROM ' . TABLE_PREFIX . 'cennik WHERE id_oferty = "' . $offerid . '"';
        self::$baza->query($sql_delete_current);

        $today = date('Y-m-d H:i:s');
        $sql_delete_future_history = 'DELETE FROM ' . TABLE_PREFIX . 'cenniki_history 
                                      WHERE id_oferty = "' . $offerid . '" 
                                        AND cena_od >= "' . $today . '"';
        self::$baza->query($sql_delete_future_history);
    }
    
    /**
     * Pobiera WSZYSTKIE zdefiniowane warianty cenowe i okresy dla produktu
     * @param array $product Tablica produktu z mongo
     * @param string $product_type Typ produktu z Mongo
     * @return array|false
     */
    public static function _getAllProductPrices($product, $product_type) {
        $ceny_wszystkie = array();

        switch ($product_type) {
            case 'karta':
            case 'ubezpieczenie':
            case 'produkt_wirtualny':
                $cena_key = ($product_type == 'ubezpieczenie') ? 'cena_ubezpieczenia' : 'sprzedaz';
                if (!isset($product['sprzedaz_od']['sec']) || !isset($product[$cena_key]['brutto'])) return false;

                $dataod = new DateTime(date('Y-m-d', $product['sprzedaz_od']['sec']));
                $datado = new DateTime(date('Y-m-d', $product['sprzedaz_do']['sec']));
                $ceny_wszystkie[] = array('wariant' => 0, 'cena' => $product[$cena_key]['brutto'], 'cena_od' => $dataod, 'cena_do' => $datado);
                break;
            
            case 'karnet':
            case 'voucher':
                if (!isset($product['cena']) || !is_array($product['cena'])) return false;
                
                $ceny_okresowe = $product['cena'];
                foreach ($ceny_okresowe as $okres) {
                    foreach ($okres as $wariant_key => $dane_ceny) {
                        if (!isset($dane_ceny['data_od']['sec'])) continue;

                        $dataod = new DateTime(date('Y-m-d', $dane_ceny['data_od']['sec']));
                        $datado = new DateTime(date('Y-m-d', $dane_ceny['data_do']['sec']));
                        $bsid = isset($product['wariant_cenowy'][$wariant_key]['bsid']) ? $product['wariant_cenowy'][$wariant_key]['bsid'] : $wariant_key;
                        
                        $ceny_wszystkie[] = array('wariant' => $bsid, 'cena' => $dane_ceny['cena'], 'cena_od' => $dataod, 'cena_do' => $datado);
                    }
                }
                break;
        }

        return !empty($ceny_wszystkie) ? $ceny_wszystkie : false;
    }

    /**
     * Pobiera cenę produktu dla danej daty
     * @param array $product Tablica produktu z mongo
     * @param string $product_type Typ produktu z Mongo
     * @param DateTime $date Data dla której chcemy ustalić cenę
     * @return boolean
     */
    public static function _getProductPrice4date($product, $product_type, $date) {
        $date = new DateTime($date);
        $cena = false;
        switch ($product_type) {
            case 'karta':
                $dataod = new DateTime(date('Y-m-d', $product['sprzedaz_od']['sec']));
                if ($dataod > $date)
                    return false;
                $datado = new DateTime(date('Y-m-d', $product['sprzedaz_do']['sec']));
                if ($datado < $date)
                    return false;
                $cena = array('wariant' => 0, 'cena' => $product['sprzedaz']['brutto'], 'cena_od' => $dataod, 'cena_do' => $datado);
                break;
            case 'karnet':
                $ceny = $product['cena'];
                foreach ($ceny as $k => $v) {
                    foreach ($v as $kk => $vv) {
                        $dataod = new DateTime(date('Y-m-d', $vv['data_od']['sec']));
                        if ($dataod > $date)
                            break;
                        $datado = new DateTime(date('Y-m-d', $vv['data_do']['sec']));
                        if ($datado < $date)
                            break;
                        $bsid = isset($product['wariant_cenowy'][$kk]['bsid']) ? $product['wariant_cenowy'][$kk]['bsid'] : 'default';
                        $cena[] = array('wariant' => $bsid, 'cena' => $vv['cena'], 'cena_od' => $dataod, 'cena_do' => $datado);
                    }
                    if (is_array($cena))
                        break;
                }
                break;
            case 'voucher':
                // ... (podobna logika jak dla karnetu)
                break;
            case 'ubezpieczenie':
                // ...
                break;
            case 'produkt_wirtualny':
                // ...
                break;
        }
        return $cena;
    }
    
    /**
     * Wyciąganie detali produktu z oferty Mongo
     * @param array $MongoOffer Oferta mongo
     * @param string $ptype Nazwa produktu
     * @param string $identyfikator Identyfikator produktu w systemie
     * @return boolean
     */
    
    public static function ProductOfferDetails($MongoOffer, $ptype, $identyfikator) {
        if ($MongoOffer[$ptype]) {
            foreach ($MongoOffer[$ptype] as $product) {
                if ($product['identyfikator'] === $identyfikator) {
                    return $product;
                }
            }
        }
        return false;
    }
    
    /**
     * Pobiera nazwy partnerów (ośrodków) dla danej oferty
     * @param string $id_oferty
     * @return boolean|array
     */
    public static function GetOfferPartners($id_oferty){
        if(!Mymongo::isValidMongoid($id_oferty)) return false;
        if(!self::$active) self::activate();
        $sql_r = 'SELECT p.id, p.shortname FROM '.TABLE_PREFIX.'partners p JOIN '.TABLE_PREFIX.'partners_offers po ON(po.id_partnera=p.id) WHERE po.id_oferty="'.$id_oferty.'"';
        self::$baza->query($sql_r);
        while($row = self::$baza->fetch_row()){
//            $ret[$row['id']] = $row['shortname'];
            $ret[] = $row;
        }
        
        return $ret;
    }
    
    /**
     * @param array $danein Tablica asocjacyjna z danymi. Wystarczą 2 dane (ale nie vats i vatw). Struktura tablicy<br>
     * float vatw => wartość VAT ,<br>
     * float brutto => wartość brutto<br>
     * float vats => stawka vat (0.23, 0.08 itd),<br>
     * float netto => wartość netto<br>
     * @param string $ret int, float, string typ danych na zwrotce. Dokładność do 2 miejsc po przcinku (int|string * 100)
     * @return array Struktura taka jak na wejściu
     */
    public static function VAT(array $danein, $ret = 'int'){
    $wynik = array();
    $wynikr = array();
    $l = array(
        'vatw'=>false,
        'brutto'=>false,
        'vats'=>false,
        'netto'=>false
    );
    $wagi = array(
        'vatw'=>1,
        'brutto'=>2,
        'vats'=>4,
        'netto'=>8
    );

    foreach ($danein as $k => $v){
        $l[$k]= $v;
    }
    $sum = 0;
    foreach ($l as $k => $v){
        $sum += (false === $v ? 0 : $wagi[$k]);
    }

    switch ($sum){
        case 3: // wartość VAT i brutto
            $wynik['netto'] = $l['brutto']-$l['vatw'];
            $wynik['brutto'] = $l['brutto'];
            $wynik['vatw'] = $l['vatw'];
            $wynik['vats'] = ($l['brutto']/($l['brutto']-$l['vatw']))-1;
            $wynik['m'] = 3;
            break;

        case 6: // brutto i stawka vat
            $wynik['netto'] = $l['brutto']/(1+floatval($l['vats']));
            $wynik['brutto'] = $l['brutto'];
            $wynik['vatw'] = $l['brutto']-$wynik['netto'];
            $wynik['vats'] = floatval($l['vats']);
            $wynik['m'] = 6;
            break;

        case 9: // netto i wartość VAT
            $wynik['netto'] = $l['netto'];
            $wynik['brutto'] = $l['netto']+$l['vatw'];
            $wynik['vatw'] = $l['vatw'];
            $wynik['vats'] = $l['vatw']/$l['netto'];
            $wynik['m'] = 9;
            break;

        case 10: /// netto i brutto
            $wynik['netto'] = $l['netto'];
            $wynik['brutto'] = $l['brutto'];
            $wynik['vatw'] = $l['brutto']-$l['netto'];
            $wynik['vats'] = ($l['brutto']/$l['netto'])-1;
            $wynik['m'] = 10;
            break;

        case 12: // netto i stawka vat
            $wynik['netto'] = $l['netto'];
            $wynik['brutto'] = $l['netto']*(1+floatval($l['vats']));
            $wynik['vatw'] = $l['netto']*floatval($l['vats']);
            $wynik['vats'] = floatval($l['vats']);
            $wynik['m'] = 12;
            break;
    }

    switch ($ret) {
        default:
        case 'int':
            foreach($wynik as $k => $w){
                if($w === 'zw' OR $w === 'zw.') {
                    $wynikr[$k] = 'zw';
                } else {
                    $wynikr[$k] = intval(round($w * 100));
                }
            }
        break;
        case 'string':
            foreach($wynik as $k => $w){
                if($w === 'zw' OR $w === 'zw.') {
                    $wynikr[$k] = 'zw';
                } else {
                    $wynikr[$k] = strval(round($w,2));
                }
            }
            break;
        case 'float':
            foreach($wynik as $k => $w){
                if($w ==='zw' OR $w ==='zw.') {
                    $wynikr[$k] = 'zw';
                } else {
                    $wynikr[$k] = round($w,2);
                }
            }
            break;
    }


  $wynikr['src'] = $wynik;
  $wynikr['ret'] = $ret;
  $l = array(
        'vatw'=>false,
        'brutto'=>false,
        'vats'=>false,
        'netto'=>false
    );
  return $wynikr;  
}

    /*
     * Przykładowa tablica fields
     * $fields = array(
     *      'wartosc_zamowienia_brutto' => 1,
     *      'zamowienia'                => array(
     *          'parentid' => 1,
     *          'karta' => array(
     *              'identyfikator' => 1,
     *          )
     *      ),
     *  );
     */
    public static function GetArrayData( $fields, $array, $row_key = false )
    {
        if ( ! is_array( $fields ) || ! is_array( $array ) )
        {
            return false;
        }

        foreach ( $fields as $field_name => $value )
        {
            if ( isset( $array[ $field_name ] ) )
            {
                if ( is_array( $value ) )
                {
                    $rec_result = self::GetArrayData( $value, $array[ $field_name ], key( $array[ $field_name ] ) );
                    if ( $rec_result )
                    {
                        $result[ $field_name ] = $rec_result;
                    }
                }
                else
                {
                    if ( $value === 1 )
                    {
                        $result[ $field_name ] = $array[ $field_name ];
                    }
                }
            }
            else
            {
                foreach ( $array as $row_key => $row )
                {
                    $rec_result = self::GetArrayData( array( $field_name => $value ), $row, $row_key );
                    if ( $rec_result )
                    {
                        $result[ $row_key ][ $field_name ] = $rec_result[ $field_name ];
                    }
                }
            }
        }
        return $result;
    }
    
    /**
     * Zaokrąglanie do dwóch miejsc
     * @param type $val
     * @return type
     */
    public static function rn( $val ) { 
        return round( $val, 2, PHP_ROUND_HALF_UP );
    }
    
    public static function AddCC( $amount )
    {
        if ( CURRENCYCODESIDE == 'left' )
        {
            $amount = CURRENCYCODE . ' ' . $amount;
        }
        else
        {
            $amount.= ' ' . CURRENCYCODE;
        }
        
        return $amount;
    }
    
    public static function CalcNf( $num1, $num2, $operation = 'add' )
    {
        switch ( $operation )
        {
            default   : return false;
            case 'add': return number_format( ($num1 + $num2), 2, '.', '' );
            case 'sub': return number_format( ($num1 - $num2), 2, '.', '' );
            case 'mul': return number_format( ($num1 * $num2), 2, '.', '' );
            case 'div': return number_format( ($num1 / $num2), 2, '.', '' );
        }
    }
    
    public static function GetIdProductSegment( $id_product, $segment = 1 )
    {
        
        $exp = '';
        
        for ( $i = 0; $i < $segment; $i++ )
        {
           if ( $i === 0 )
           {
               $exp.= '(\d+';
           }
           else
           {
               $exp.= '\_\d+';
           }
        }
        
        $exp.= ')';
        
        if ( preg_match( $exp, $id_product, $match ) )
        {
            return $match[0];
        }
        else
        {
            return false;
        }
    }
    
    /**
     * Zwraca ilość pozostałych dni do wykorzystania na karnecie
     * @param int $id
     * @return type
     */
    public static function GetTicketDaysLeft( $ticket_id ) 
    {
        if ( ! self::$active ) 
        {
            self::activate();
        }
        $sql2 = 'SELECT kt.id ,kz.dni as liczba_dni FROM ts_karnety kt '
                .'LEFT JOIN exchange_ticket_codes etc ON kt.id = etc.ticket_id AND etc.esp_type = "ticket" '
                .'LEFT JOIN exchange_ticket_codes_state kz ON kz.exchange_ticket_codes_id = etc.id '
                .'WHERE kt.status > 0 '
                .'AND kt.id =' . $ticket_id;
        $days_left = self::$baza->select( $sql2 );
        if ( ! $days_left )
        {
           return false;
        } else
        {
            if(!$days_left[0]['liczba_dni'])
            {
                return 'bd';
            }
            return intval( $days_left[0]['liczba_dni'] ) ?? 0;
        }
    }
    
    /**
     * Pobiera dane o walucie
     * @param array $fields Pola do pobrania
     * @param int $id Id waluty
     * @return array Dane waluty
     */
    public static function getCurrencyData( $fields, $id )
    {
        if ( ! self::$active ) 
        {
            self::activate();
        }
        
        $where = array( array( 'id', $id, 'INT' ) );
        
        $data = self::$baza->get_filtered_rows( 'currency', $fields, $where );
        
        if ( $data )
        {
            return array_pop( $data );
        }
        else
        {
            return false;
        }
    }
    
    public static function transportMetaData( $hash, $fields = false )
    {
        if ( ! self::$active ) 
        {
            self::activate();
        }

        $sql = 'SELECT meta.properties, meta.value, pp.parent_id as kurier_id ';
        $sql.= 'FROM ' . TABLE_PREFIX . 'partners_products_meta AS meta ';
        $sql.= 'JOIN ' . TABLE_PREFIX . 'partners_products as pp ';
        $sql.= 'ON meta.id_parent=pp.id ';
        $sql.= 'AND pp.hash = "' . $hash . '" ';
        $sql.= 'AND pp.current = 1 ';
        
        if ( $fields )
        {
            if ( is_array( $fields ) )
            {
                foreach ( $fields as $field )
                {
                    $sql.= 'AND meta.properties = "' . $field . '" ';
                }
            }
            else
            {
                $sql.= 'AND meta.properties = "' . $fields . '" ';
            }
        }

        self::$baza->query( $sql );

        while( $row = self::$baza->fetch_row() )
        {
            $result['kurier_id'] = $row['kurier_id'];
            $result[ $row['properties'] ] = $row['value'];
        }
        
        return $result ? $result : false;
    }
    
    public static function getPackData( $identyfikator )
    {
        if ( ! self::$active )
        {
            self::activate();
        }
        
        $sql = 'SELECT ';
        $sql.= ' p.id ';
        $sql.= ', p.ograniczone ';
        $sql.= ', p.wylacznosc ';
        $sql.= ', p.identyfikator ';
        $sql.= ', pl.value AS nazwa ';
        $sql.= 'FROM ' . TABLE_PREFIX . 'pakiety AS p ';
        $sql.= 'JOIN ' . TABLE_PREFIX . 'pakiety_langs AS pl ';
        $sql.= 'ON pl.parentid = p.id ';
        $sql.= 'AND p.identyfikator = "' . $identyfikator . '" ';
        $sql.= 'AND pl.langid = ' . LANG . ' ';
        $sql.= 'AND pl.property = "nazwa" ';
        return self::$baza->select_r( $sql );
    }
    
    public static function checkProductInPack( $pack_id, $p_type )
    {
        if ( ! self::$active )
        {
            self::activate();
        }
        
        $sql = 'SELECT kupon_id, produkt_id FROM ' . TABLE_PREFIX . 'pakiety_items ';
        $sql.= 'WHERE parentid=' . $pack_id . ' AND p_type ="' . $p_type . '"';
        return self::$baza->select_r( $sql );
    }
    
    /**
     * Pobiera listę zawieszonych produktów (wg id oferty lub produktu)
     * @param type $idnt Identyfikator
     * @param type $idntype Rodzaj identyfikatora: <br>offerid - Identyfikator oferty<br>prid - Identyfikator produktu, <br>ptype - typ produktu
     */
    public static function GetOfferSuspendedProduct($idnt, $idntype = 'offerid', $offerid = null){
        if ( ! self::$active ) self::activate();
        $ret = [];
        switch($idntype){
            case 'offerid':
                $sql = 'SELECT * FROM '.TABLE_PREFIX.'produkty_offline WHERE oferta = "'.$idnt.'"';
                self::$baza->query($sql);
                while($r = self::$baza->fetch_row()){
                    $ret[$r['identyfikator']] = $r['typ_produktu'];
                }
                break;
            
            case 'prid':
                if(empty($offerid)) {
                    $sql = 'SELECT * FROM '.TABLE_PREFIX.'produkty_offline WHERE identyfikator = "'.$idnt.'"';
                } else {
                    $sql = 'SELECT * FROM '.TABLE_PREFIX.'produkty_offline WHERE identyfikator = "'.$idnt.'" AND oferta="' .$offerid . '"';
                }
                self::$baza->query($sql);
                while($r = self::$baza->fetch_row()){
                    $ret[$r['identyfikator']] = $r['typ_produktu'];
                }
                break;
            case 'ptype':
                $sql = 'SELECT * FROM '.TABLE_PREFIX.'produkty_offline WHERE typ_produktu = "'.$idnt.'"';
                self::$baza->query($sql);
                while($r = self::$baza->fetch_row()){
                    $ret[$r['identyfikator']] = $r['typ_produktu'];
                }
                break;
        }
        
        return $ret;
    }
    
    public static function makeErrorsString( $errors_string, $error_msg )
    {
        if ( $errors_string )
        {
            $errors_string.= '<br>' . $error_msg;
        }
        else
        {
            $errors_string = $error_msg;
        }
        
        return $errors_string;
    }
    
    public static function GetSystemSettings(){
        if ( ! self::$active ) self::activate();
        
        $sql = 'SELECT * FROM '.TABLE_PREFIX.'settings';
        self::$baza->query($sql);
        while($r = self::$baza->fetch_row()){
            $ret[$r['variable']] = $r;
        }
        return $ret;
    }

    public static function reCC( $amount, $rate, $currency_id )
    {
        if ( ! self::$active ) self::activate();

        $sql = 'SELECT currency_code, currency_code_side FROM ' . TABLE_PREFIX . 'currency ';
        $sql.= 'WHERE id = ' . $currency_id;
        $res = self::$baza->select_r( $sql );

        if ( $res['currency_code'] )
        {
            if ( $res['currency_code_side'] == 'left' )
            {
                return $res['currency_code'] . ' ' . number_format($amount * $rate, 2);
            }
            else
            {
                return number_format($amount * $rate, 2) . ' ' . $res['currency_code'];
            }
        }
        else
        {
            return false; 
        }
    }

    public static function getTransportName($kurier_id, $hash, $lang, $array = false )
    {
        $partner_data = self::GetPartnerData($kurier_id);

        if(!$partner_data)
        {
            return false;
        }

        $product_data = self::GetPartnerProductLangs($hash, 'hash');

        if(!$product_data['nazwa'][$lang])
        {
            return false;
        }

        if($array)
        {
            return $ret = array(
                'nazwa'         => $partner_data['name'],
                'produkt_nazwa' => $product_data['nazwa'][$lang],
                );            
        }

        return $partner_data['name'] . ' ' . $product_data['nazwa'][$lang]; 
    }

    public static function getBonData()
    {
        if ( ! self::$active ) self::activate();
        $sql = 'SELECT id, name, shortname FROM ' . TABLE_PREFIX . 'partners ';
        $sql.= 'WHERE id = ' . PAYBON_ID;
        $res = self::$baza->select_r($sql);

        return $res;
    }
    
    public static function getGrupyProduktowe($idoferty){
        self::activate();
        $sql_r = 'SELECT gpl.parentid as id, gpl.value as nazwa FROM ' . TABLE_PREFIX . 'grupy_produktowe_langs gpl LEFT JOIN '.TABLE_PREFIX.'grupy_produktowe gp ON gp.id = gpl.parentid WHERE gp.id_oferty="' . $idoferty . '" AND gpl.property = "nazwa" AND gpl.langid='.LANG;
        $r= self::$baza->query($sql_r);
        while($row = self::$baza->fetch_row()){
            $res[$row['id']] = $row;
        }
        return $res;
    }

    public static function prepareDir($dir)
    {
        if (strpos($dir, '/') !== 0) {
            $dir = PROJECTDIR .'/' . str_replace(['.', '..'],'', $dir);
        }
        if (!file_exists($dir)) {
            mkdir($dir, 0775, true);
            chmod($dir, 0775);
        }
    }

    public static function prepareNumber(int $long = 8)
    {
        $bytes = random_bytes($long/2);
        return bin2hex($bytes);
    }

    public static function array_size($arr)
    {
        return round(self::calcsize($arr) / 1048576, 2);
    }

    public static function arrayStringSize($arr)
    {
        return round(strlen(json_encode($arr)) / 1048576, 2);
    }

    public static function checklangs($data) {
        if (!is_array($data)) {
            return $data;
        }
        if ('' == $first = current($data)) {
            while ($first == '' AND $first != false) {
                $first = next($data);
            }
        }

        if ($first == '') {
            return $data;
        }
        reset($data);
        foreach ($data as $k => $v) {
            if ($v == '')
                $data[$k] = $first;
        }
        return $data;
    }

    private static function calcsize($arr)
    {
        $byte = 0;
        foreach ($arr as $key => $val) {
            $byte += is_array($val) ? self::calcsize($val) : mb_strlen($val);
        }
        return $byte;
    }


    public static function imageFileToBase64(string $path): string
    {
        $path = PROJECTDIR . $path;
        if (!file_exists($path)) {
            return '';
        }

        $imageData = file_get_contents($path);
        if (empty($imageData)) {
            return '';
        }

        $mimeType = mime_content_type($path);

        if (empty($mimeType)) {
            trigger_error("Nie można rozpoznać typu MIME dla pliku", E_USER_WARNING);
            return '';
        }

        $base64 = base64_encode($imageData);
        if ($base64 === '') {
            trigger_error("Nie można zakodować danych obrazu w Base64.", E_USER_WARNING);
            return '';
        }

        return 'data:' . $mimeType . ';base64,' . $base64;
    }

    public static function GetCleanedCardsFromFile($fieldname) {
        $files = $_FILES;
        if ($files[$fieldname]['error'] !== 0 || $files[$fieldname]['size'] === 0) {
            self::$errors[] = 'Nie udało się wczytać pliku';
            return [];
        }

        $tmp_file = $files[$fieldname]['tmp_name'];
        $csv = file($tmp_file);
        if (count($csv) > 0) {
            foreach ($csv as $i => $c) {
                $cvalue = trim(rtrim($c,", \n\r\t\v"));
                if (!empty($cvalue)) {
                    $csv[$i] = $cvalue;
                } else {
                    unset($csv[$i]);
                }
            }
        }

        return $csv;
    }
}