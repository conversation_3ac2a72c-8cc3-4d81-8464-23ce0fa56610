<?php

use MongoDB\BSON\ObjectId as MongoId;

include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');

/**
 * @@@Partner@@@
 * Metody do uprawnień:
 * @@home: startowa,
 * cards:Lista kart,
 * order:zamowienia,
 * mydata:dane partnera,
 * history:historia zamowień,
 * faktury:lista faktur partnera,
 * raports: raporty,
 * statystyki: statystyki ś<PERSON>żynek,
 * kasa: Panel kasjera,
 * sumorder: Zamowienie a1,
 * acceptorder: Zamowienie s2,
 * changemydata: <PERSON><PERSON><PERSON>,
 * ajaxhidetask: AJAX s1,
 * ajaxupdatetask: AJAX s1,
 * ajaxaddtobasket:Zamowienie AJAX s1,
 * ajaxcarddetails:Zamowienie AJAX s1,
 * ajaxorderdetails:Zamowienie AJAX s1,
 * ajaxdelfrombasket:Zamowienie AJAX s1,
 * ajaxcheckcardnumber:Sprawdz numer karty AJAX,
 * AjaxCheckMassTopUpCards:AJAX Sprawdzenie csv z listą kart,
 * ajaxfindvoucher:Znajdź voucher AJAX,
 * ajaxconfirmvoucher:Zapisz wykorzystanie vouchera AJAX,
 * ajaxupdatefaktura:Aktualizacja statusu faktury AJAX,
 * sp_ordlist:AJAX Lista zamówień,
 * sp_ticketlist:AJAX Lista karnetów,
 * sp_cardlist:Ajax Lista kart,
 * sp_voucherlist:AJAX Lista Voucherów,
 * sp_insulist:AJAX Lista Ubezpieczń,
 * sp_productlist:AJAX Lista Vproduktów,
 * sp_rozliczenie:AJAX Rozliczenie,
 * aac:Sprawedzenie pinu i akceptacji
 * acceptoffer: Proces akceptacji oferty,
 * ajaxlistvouchers: lista voucherow partnera
 * invoice: wydruk faktury
 * @@
 *
 * Obsługa strony partnera
 * @package Application
 * @subpackage Modules
 */
class Partner extends Module
{
    //1 = partner sprzedazowy
    //2 = osrodek
    //3 = ubezpieczenie
    //6 = franczyzowy
    //9 = wirtualny produkt
    public $statusy = [
        -100 => "Skasowany",
        -1 => "Nowy",
        0 => "Zużyty",
        1 => "Aktywny",
        3 => "w produkcji (3)",
        4 => "w produkcji (4)"
    ];
    public $app = null;
    public $tpl_data = null;
    protected $alerts = array();
    protected $sdb = null;
    protected $post = null;
    protected $get = null;
    public $header_data = array();
    public $dash_data = array();
    public $ptop_data = array();
    public $pfooter_data = array();
    public $partner = array();
    public $ptype = array(1 => 'partner_sp', 2 => 'osrodek', 3 => 'ubezpieczenie', 6 => 'partner_fr', 9 => 'vprodukt');
    // menu[uprawnienia_usera[typ_partnera]];
    private $menu = array(
        3 => array(
            2 => array(
                'pulpit' => 1,
                'raporty' => 1,
//                'oferty' => 1,
                'kasa' => 1,
                'dane' => 1,
//                'statystyki' => 1,
//                'statystyki-sniezynki' => 1,
//                'statystyki-kupony' => 1
                'stankarty' => 1,
                'stanvouchera' => 1,
//                'fakturywplista'=>1,
//                'historiawp'=>1,
//                'fakturywp' => 1,
                'fakturyLista' => 1,
                'fakturowniaLista' => 1
            ),
            1 => array(
//                'pulpit' => 1,
                'zamowieniah' => 1,
                'zamowienian' => 1,
                'raporty' => 1,
//                'oferty' => 1,
                'kasa' => 1,
                'dane' => 1,
//                'statystyki' => 1,
//                'statystyki-kupony' => 1,
                'stankarty' => 1,
                'stanvouchera' => 1,
                'rozliczeniawp' => 1,
                'fakturywp' => 1
            ),
            6 => array(
                'pulpit' => 1,
//                'zamowienia' => 1,
                'raporty' => 1,
                'oferty' => 1,
//                'kasa' => 1,
                'dane' => 1,
//                'fakturywplista'=>1,

            ),
            3 => array(
                'pulpit' => 1,
                'raporty' => 1,
                'oferty' => 1,
                'sprawdzubp' => 1,
                'dane' => 1
            ),
            8 => array(
                'pulpit' => 1,
                'raporty' => 1,
//                'oferty' => 1,
//                'sprawdzubp' => 1,
                'dane' => 1
            ),
            9 => array(
                'pulpit' => 1,
                'raporty' => 1,
                'dane' => 1,
                'oferty' => 1,
                'kasa' => 1
            )
        ),
        50 => array(
            2 => array(
                'kasa' => 1
            ),
            1 => array(
                'kasa' => 1,
                'stankarty' => 1
            ),
            6 => array(
                'kasa' => 1
            ),
            3 => array(),
            9 => array(
                'kasa' => 1
            )
        )
    );

    public function __construct($app)
    {
        $this->app = &$app;
        $this->sdb = Database::activate();
        $this->post = $this->app->postparam;
        $this->get = $this->app->urlparam;
        $this->_r('_getPartnerInfo');
        $this->menu[User::GetUserPerm()][$this->partner['partner']['partnertype']]['stanvouchera'] = $this->partner['partner']['voucherTab'];
        $this->dash_data['menu'] = $this->menu[User::GetUserPerm()][$this->partner['partner']['partnertype']];
        $this->alerts = $_SESSION['alerts'];
        unset($_SESSION['alerts']);
        switch ($app->urlparam['a']) {
            default:
                if ((int)User::GetUserPerm() === 3) {
                    if (array_key_exists('zamowienian', $this->menu[User::GetUserPerm()][$this->partner['partner']['partnertype']])) {
                        $this->_r('order');
                    } else {
                        $this->_r('home');
                    }
                }
                if ((int)User::GetUserPerm() === 50) {
                    $this->_r('kasa');
                }
                break;
            case 'cards':
                $this->_r('cards');
                break;
            case 'order':
                if (array_key_exists('zamowienian', $this->menu[User::GetUserPerm()][$this->partner['partner']['partnertype']])) {
                    $this->_r('order');
                } else {
                    $this->_r('home');
                }
                break;
            case 'offers':
                if (array_key_exists('oferty', $this->menu[User::GetUserPerm()][$this->partner['partner']['partnertype']])) {
                    $this->_r('DisplayOffersList');
                } else {
                    $this->_r('home');
                }
                break;
            case 'kasa':
                if (array_key_exists('kasa', $this->menu[User::GetUserPerm()][$this->partner['partner']['partnertype']])) {
                    $this->_r('kasa');
                } else {
                    $this->_r('home');
                }
                break;
            case 'mydata':
                $this->_r('mydata');
                break;
            case 'contact'             :
                $this->_r('contact');
                break;
            case 'history'             :
                $this->_r('history');
                break;
            case 'raports'             :
                $this->_r('raports');
                break;
            case 'statystyki'          :
                $this->_r('statystyki');
                break;

            case 'fakturyLista'         :
                $this->_r('FakturyOsrodka');
                break;
            case 'fakturowniaLista'     :
                $this->_r('FakturowniaOsrodka');
                break;
            case 'faktury'             :
                $this->_r('faktury', $_GET['id']);
                break;
            case 'fakturywplista'      :
                $this->_r('FakturyWPLista');
                break;
            case 'rozliczeniawp'       :
                $this->_r('rozliczeniawp', $_GET['id']);
                break;
            case 'getrozliczeniewp'    :
                $this->_r('getrozliczeniewp', $_POST['id']);
                break;
            case 'listarozliczeniawp'  :
                $this->_r('listarozliczeniawp');
                break;
            case 'sumorder'            :
                $this->_r('sumorder');
                break;
            case 'acceptorder'         :
                $this->_r('acceptorder');
                break;
            case 'changemydata'        :
                $this->_r('changemydata');
                break;
            case 'ajaxhidetask'        :
                $this->_r('ajaxhidetask');
                break;
            case 'ajaxupdatetasks'     :
                $this->_r('ajaxupdatetasks');
                break;
            case 'ajaxgetbasketcont'   :
                $this->_r('ajaxgetbasketcont');
                break;
            case 'ajaxaddtobasket'     :
                $this->_r('ajaxaddtobasket');
                break;
            case 'ajaxcarddetails'     :
                $this->_r('ajaxcarddetails');
                break;
            case 'ajaxorderdetails'    :
                $this->_r('ajaxorderdetails');
                break;
            case 'ajaxdelfrombasket'   :
                $this->_r('ajaxdelfrombasket');
                break;
            case 'ajaxcheckcardnumber' :
                $this->_r('ajaxcheckcardnumber');
                break;
            case 'ajaxcheckmasstopupcards':
                $this->_r('ajaxcheckmasstopupcards');
                break;
            case 'ajaxfindvoucher'     :
                $this->_r('ajaxfindvoucher');
                break;
            case 'ajaxconfirmvoucher'  :
                $this->_r('ajaxconfirmvoucher');
                break;
            case 'ajaxupdatefaktura'   :
                $this->_r('ajaxupdatefaktura');
                break;
            case 'ajaxlistvouchers'   :
                $this->_r('ajaxlistvouchers', $_GET);
                break;
            case 'ajaxlistfakturywp'   :
                $this->_r('ajaxlistfakturywp', $_GET);
                break;
            case 'ajaxlistkartyosrodek'   :
                $this->_r('KartyOsrodek', $_GET);
                break;
            case 'ajaxlistfakturylista'   :
                $this->_r('FakturyOsrodkaDt', $_GET);
                break;
            case 'ajaxlistfakturownialista'   :
                $this->_r('FakturowniaOsrodkaDt', $_GET);
                break;
            case 'ajaxdownloadzip'   :
                $this->_r('getZip', $_GET);
                break;
            case 'returnvoucher'   :
                $this->_r('ReturnVoucher', $_POST);
                break;

            case 'sp_ordlist'          :
            case 'os_ordlist'          :
            case 'ub_ordlist'          :
            case 'vp_ordlist'          :
                $this->_r('AjaxOrderList', $_GET, 20, 1);
                break;

            case 'bestbyuers'   :
                $this->_r('AjaxBestBuyers', $_GET, 20, 1);
                break;

            case 'sp_ticketlist'       :
            case 'os_ticketlist'       :
                $this->_r('AjaxTicketList', $_GET, 20, 1);
                break;
            case 'sp_cardlist'         :
                $this->_r('AjaxCardList', $_GET, 20, 1);
                break;

            case 'sp_voucherlist'      :
            case 'os_voucherlist'      :
                $this->_r('AjaxVoucherList', $_GET, 20, 1);
                break;

            case 'sp_insulist'         :
            case 'ub_insulist'         :
                $this->_r('AjaxInsuList', $_GET, 20, 1);
                break;

            case 'sp_productlist'      :
            case 'vp_productlist'      :
                $this->_r('AjaxProductList', $_GET, 20, 1);
                break;


            case 'os_couponlist'       :
            case 'ub_couponlist'       :
            case 'vp_couponlist'       :
                $this->_r('AjaxCouponList', $_GET, 20, 1);
                break;

            case 'os_returnslist'           :
                $this->_r('AjaxReturnsList', 'osrodek', $_GET);
                break;
            case 'vp_returnslist'           :
                $this->_r('AjaxReturnsList', 'vprodukt', $_GET);
                break;
            case 'os_raport'           :
                $this->_r('AjaxRaport', 'osrodek', $_GET);
                break;
            case 'sp_raport'           :
                $this->_r('AjaxRaport', 'sprzedaz', $_GET);
                break;
            case 'ub_raport'           :
                $this->_r('AjaxRaport', 'ubezpieczenie', $_GET);
                break;
            case 'vp_raport'           :
                $this->_r('AjaxRaport', 'vprodukt', $_GET);
                break;
            case 'os_rozliczenie'      :
                $this->_r('ShowRozliczenie', 'os');
                break;
            case 'os_rozliczeniewp'      :
                $this->_r('ShowRozliczenie', 'oswp');
                break;
            case 'sp_rozliczenie'      :
                $this->_r('ShowRozliczenie', 'sp');
                break;
            case 'ub_rozliczenie'      :
                $this->_r('ShowRozliczenie', 'ub');
                break;
            case 'vp_rozliczenie'    :
                $this->_r('ShowRozliczenie', 'vp');
                break;

            case 'ajaxofferslist'    :
                $this->_r('AjaxOffersList', $_GET, 10, 1);
                break;
            case 'viewoffer'         :
                $context = $this->ptype[$this->partner['partner']['partnertype']];
                $this->_r('ViewOffer', $_GET['id'], $context);
                break;
            case 'aac'               :
                $this->_r('AjaxAuthCheck');
                break;
            case 'acceptoffers'      :
                $this->_r('AcceptOffer', 'acceptoffer');
                break;
            case 'rejectoffers'      :
                $this->_r('AcceptOffer', 'rejectoffer');
                break;
            case 'invoice'           :
                $this->_r('Invoice', $_GET['id']);
                break;
            case 'chckinsurance'           :
                $this->_r('Chckinsurance');
                break;
            case 'ajaxchckinsurance'           :
                $this->_r('AjaxCheckCardUse', $_GET, 10, 1);
                break;

            ################################################################################
            case 'atb':
                $this->AjaxAddToBasket();
                break;

            case 'testcheck':
                abort(404);
                break;

            case 'basket':
                Tools::PA($_SESSION['basket']);
                die();
                break;

            case 'u':
                echo '<head><title>Usuwanie koszyka</title></head>';
                unset($_SESSION['basket']);
                die('Usunięto koszyk');
                break;

            case 'resetbasket':
                $this->app->SetDecorator('Ajax');
                unset($_SESSION['basket']);
                $this->app->ADD('response', json_encode(array('status' => 'ok')));
                $this->return = true;
                break;

            case 'psp':
                abort(404);
                break;

            case 'chargewallet':
                $this->_r('AjaxChargeWallet');
                break;

            case 'rozliczwp':
                $this->_r('AjaxRozliczPartneraWP');
                break;
            case 'historiawp':
                $this->_r('historiawp');
                break;
            case 'stankarty':
                $this->_r('StanKarty');
                break;
            case 'stanvouchera':
                $this->_r('StanVouchera');
                break;

            case 'voucherinfo':
                $this->_r('Voucherinfo');
                break;
            case 'getcardstatus':
                $this->_r('GetCardStatus');
                break;
            case 'getcardhistory':
                $this->_r('GetCardHistory');
                break;
            ################################################################################
        }
    }

    public function __destruct()
    {
        $_SESSION['alerts'] = $this->alerts;
    }

    public function _getPartnerInfo()
    {
        $this->partner['partner'] = Tools::GetPartnerData(INSTALLATION);
        $this->partner['meta'] = Tools::GetPartnerMetadata(INSTALLATION);
        $this->partner['wp'] = Tools::GetPartnerWPData(INSTALLATION);
        $this->partner['partner']['type_name'] = $this->sdb->get_field_val('partner_type', 'name',
            array(array('id', $this->partner['partner']['partnertype'], 'INT')));
        $this->partner['partner']['voucherTab'] = 0;
        if ($this->partner['partner']['partnertype'] == 2) {
            $this->partner['partner']['voucherTab'] = 1;
        } else {
            $vouchersExist = $this->sdb->select('SELECT id FROM ' . TABLE_PREFIX . 'vouchery WHERE installation=' . INSTALLATION . ' LIMIT 1');
            if (!empty($vouchersExist)) {
                $this->partner['partner']['voucherTab'] = 1;
            }
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Funkcje Ajax
    ////////////////////////////////////////////////////////////////////////////////

    public function AjaxUpdateTasks()
    {
        $post = $this->app->postparam;
        $response['ok'] = true;
        if ($post['time']) {
            Sms::Setstatus(USER::GetUserID(), $post['time']);
            $response['ok'] = true;
        } else {
            $response['ok'] = false;
        }
        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function AjaxHideTask()
    {
        $post = $this->app->postparam;
        if ($post['id']) {
            Sms::HideNote($post['id']);
            $response['ok'] = true;
        } else {
            $response['ok'] = false;
        }
        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    /**
     * Sprawdzanie i zwracanie numeru karty
     * Dane w POST:
     * @param string $card_serial Numer seryjny karty
     * @param string $user 'True' szukamy po wszytkich kartach, FALSE - szukamy po kartach usera aktualnego partnera
     */
    public function AjaxCheckCardNumber()
    {
        $post = $this->app->postparam;
        $reponse['errors'] = array();
        if ($post['card_serial']) {
            if ($post['user'] === 'true') {
                $check = Karta::CheckCard($post['card_serial'], false);
            } else {
                $check = Karta::CheckCard($post['card_serial'], true, USER::GetUserID(), INSTALLATION);
            }

            if ($check['cid']) {
                $response['card_id'] = $check['cid'];
            } else {
                if ($check['error']) {
                    $response['errors'][] = $check['error'];
                    $response['checklink'] = 1;
                } else {
                    $response['errors'][] = 'Nie odnaleziono karty';
                }
            }
        } else {
            $reponse['errors'][] = 'Nie otrzymano numeru karty!';
        }
        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }


    /**
     * Szukanie vouchera
     */
    public function AjaxFindVoucher()
    {
        $post = $this->app->postparam;
        $reponse['errors'] = array();

        if (false === preg_match('/\d{12}/', $post['vs'])) {
            $response = array("result" => false);
            $this->app->ADD('response', json_encode($response));
            $this->app->SetDecorator('Ajax');
            return;
        }
        $response = array("result" => false);
        $sql = $this->sdb->get_filtered_rows('pakiety_uzyte', '*', array(array('voucherserial', $post['vs'], 'STRING')));
        $mgo = Mymongo::activate();
        if (!empty($sql)) {
            $karty_ret = array();
            $karnety_ret = array();
            $ubezp_ret = array();
            $vprod_ret = array();
            $vouchery_ret = array();

            foreach ($sql as $item) {
                $response = array("result" => true);
                switch ($item['ptype']) {
                    case 'karty':
                        $sqlk = $this->sdb->get_row('karty', $item['id_item']);
                        $typ_karty = Tools::GetPartnerProductLangs($sqlk['typ_karty'], 'hash');
                        $produkt = $mgo->mget_product_details($sqlk['id_produktu_oferta'], 'karty');
                        $karty_ret[] = array(
                            'id' => $item['id_item'],
                            'serial' => $sqlk['serial'],
                            'typ_karty' => $typ_karty['nazwa'][LANG],
                            'img_path' => $sqlk['img_path'],
                            'nazwa' => $produkt['nazwa'][LANG],
                            'podtytul' => strip_tags($produkt['podtytul'][LANG]),
                            'opis1' => strip_tags($produkt['note1'][LANG]),
                            'opis2' => strip_tags($produkt['note2'][LANG]),
                            'zrealizowany' => $item['zrealizowany']
                        );
                        break;
                    case 'karnety':
                        $sqlk = $this->sdb->get_row('karnety', $item['id_item']);
                        $produkt = $mgo->mget_product_details($sqlk['id_produktu_oferta'], 'karnety');
                        //                            die(var_dump($produkt));
                        $ob = $produkt['obowiazuje'];
                        $obs = 'Obowiązuje od ' . date('Y-m-d', $ob['data_od']['sec']) . ' do ' . date('Y-m-d', $ob['data_do']['sec']);
                        $karnety_ret[] = array(
                            'id' => $item['id_item'],
                            'nazwa' => $produkt['nazwa'][LANG],
                            'podtytul' => strip_tags($produkt['podtytul'][LANG]),
                            'opis1' => strip_tags($produkt['note1'][LANG]),
                            'opis2' => strip_tags($produkt['note2'][LANG]),
                            'obowiazuje' => $obs,
                            'zrealizowany' => $item['zrealizowany']
                        );
                        break;
                    case 'ubezpieczenia':
                        $sqlk = $this->sdb->get_row('ubezpieczenia', $item['id_item']);
                        $produkt = $mgo->mget_product_details($sqlk['id_produktu_oferta'], 'ubezpieczenia');
                        $ubezp_ret[] = array(
                            'id' => $item['id_item'],
                            'nazwa' => $produkt['nazwa'][LANG],
                            'podtytul' => strip_tags($produkt['podtytul'][LANG]),
                            'opis1' => strip_tags($produkt['note1'][LANG]),
                            'opis2' => strip_tags($produkt['note2'][LANG]),
                            'zrealizowany' => $item['zrealizowany']
                        );
                        break;
                    case 'vouchery':
                        $sqlk = $this->sdb->get_row('vouchery', $item['id_item']);
                        $produkt = $mgo->mget_product_details($sqlk['id_produktu_oferta'], 'vouchery');
                        $vouchery_ret[] = array(
                            'id' => $item['id_item'],
                            'nazwa' => $produkt['nazwa'][LANG],
                            'podtytul' => strip_tags($produkt['podtytul'][LANG]),
                            'opis1' => strip_tags($produkt['note1'][LANG]),
                            'opis2' => strip_tags($produkt['note2'][LANG]),
                            'zrealizowany' => $item['zrealizowany']
                        );
                        break;
                    case 'produkty_wirtualne':
                        $sqlk = $this->sdb->get_row('vprodukty', $item['id_item']);
                        $produkt = $mgo->mget_product_details($sqlk['id_produktu_oferta'], 'produkty_wirtualne');
                        $vprod_ret[] = array(
                            'id' => $item['id_item'],
                            'nazwa' => $produkt['nazwa'][LANG],
                            'podtytul' => strip_tags($produkt['podtytul'][LANG]),
                            'opis1' => strip_tags($produkt['note1'][LANG]),
                            'opis2' => strip_tags($produkt['note2'][LANG]),
                            'zrealizowany' => $item['zrealizowany']
                        );
                        break;
                }
            }

            $response['karty'] = $karty_ret;
            $response['karnety'] = $karnety_ret;
            $response['ubezpieczenia'] = $ubezp_ret;
            $response['vouchery'] = $vouchery_ret;
            $response['produkty_wirtualne'] = $vprod_ret;
            $response['vserial'] = $post['vs'];
            $response['vopis'] = 'PAKIET';
        } else {
            $sql = $this->sdb->get_filtered_rows('vouchery', '*', array(array('serial', $post['vs'], 'STRING')));
            if (!empty($sql)) {
                $response = array("result" => true);
                $karty_ret = array();
                $karnety_ret = array();
                $ubezp_ret = array();
                $vprod_ret = array();
                $vouchery_ret = array();
                foreach ($sql as $item) {
                    $produkt = $mgo->mget_product_details($item['id_produktu_oferta'], 'vouchery');
                    $vouchery_ret[] = array(
                        'id' => $item['id'],
                        'nazwa' => $produkt['nazwa'][LANG],
                        'podtytul' => strip_tags($produkt['podtytul'][LANG]),
                        'opis1' => strip_tags($produkt['note1'][LANG]),
                        'opis2' => strip_tags($produkt['note2'][LANG]),
                        'zrealizowany' => $item['voucher_wydany']
                    );
                }
                $response['karty'] = $karty_ret;
                $response['karnety'] = $karnety_ret;
                $response['ubezpieczenia'] = $ubezp_ret;
                $response['vouchery'] = $vouchery_ret;
                $response['produkty_wirtualne'] = $vprod_ret;
                $response['vserial'] = $post['vs'];
                $response['vopis'] = 'VOUCHER';

            } else {
                $sql = $this->sdb->get_filtered_rows('vprodukty', '*', array(array('voucherserial', $post['vs'], 'STRING')));
                $response = array("result" => true);
                $karty_ret = array();
                $karnety_ret = array();
                $ubezp_ret = array();
                $vprod_ret = array();
                $vouchery_ret = array();
                $produkt = array();
                if (false === $sql) {
                    $sql = [];
                }
                foreach ($sql as $item) {
                    $produkt = $mgo->mget_product_details($item['id_produktu_oferta'], 'produkty_wirtualne');
                    $vprod_ret[] = array(
                        'id' => $item['id'],
                        'nazwa' => $produkt['nazwa'][LANG],
                        'podtytul' => strip_tags($produkt['podtytul'][LANG]),
                        'opis1' => strip_tags($produkt['note1'][LANG]),
                        'opis2' => strip_tags($produkt['note2'][LANG]),
                        'zrealizowany' => $item['voucher_wydany']
                    );
                }
                $response['karty'] = $karty_ret;
                $response['karnety'] = $karnety_ret;
                $response['ubezpieczenia'] = $ubezp_ret;
                $response['vouchery'] = $vouchery_ret;
                $response['produkty_wirtualne'] = $vprod_ret;
                $response['vserial'] = $post['vs'];
                $response['vopis'] = 'VPRODUCT: ' . $post['vs'];
            }
        }
        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    /**
     * Potwierdzenie wykorzystania składników vouchera
     */
    public function AjaxConfirmVoucher()
    {
        $post = $this->app->postparam;
        $reponse['errors'] = array();
        if (count($this->sdb->get_field_val('pakiety_uzyte', 'count(id)', array(array('voucherserial', $post['voucherserial'], 'STRING')))) > 0) {
            $vtype = 'zestaw';
        } else {
            if (count($this->sdb->get_field_val('vouchery', 'count(id)', array(array('serial', $post['voucherserial'], 'STRING')))) > 0) {
                $vtype = 'voucher';
            }
        }

        $wydaj = $post['wydaj'];

        if (is_array($wydaj['karnety'])) {
            $vkarnet = $wydaj['karnety'];
            Karta::CheckCard($vkarnet['card_serial'], true);
            Karnet::send2production($vkarnet['id'], 0);
            $sql_update = 'Update ' . TABLE_PREFIX . 'pakiety_uzyte SET zrealizowany = 1, data_realizacji = "' . date('Y-m-d H:i:s') . '" '
                . 'WHERE voucherserial ="' . $post['voucherserial'] . '" AND id_item=' . intval($vkarnet['id']) . ' AND ptype = "karnety"';
            $this->sdb->query($sql_update);
        }

        if (is_array($wydaj['karty'])) {
            $vkarta = $wydaj['karty'];
            Karta::CheckCard($vkarta['card_serial'], true);
            $sql_update = 'Update ' . TABLE_PREFIX . 'pakiety_uzyte SET zrealizowany = 1, data_realizacji = "' . date('Y-m-d H:i:s') . '" '
                . 'WHERE voucherserial ="' . $post['voucherserial'] . '" AND id_item=' . intval($vkarta['id']) . ' AND ptype = "karty"';
            $this->sdb->query($sql_update);
        }

        if (is_array($wydaj['vouchery'])) {
            $voucher = $wydaj['vouchery'];
            $sql = 'update ' . TABLE_PREFIX . 'vouchery SET voucher_wydany = 1, data_wydania = "' . date('Y-m-d H:i:s') . '", user_wydanie=' . User::GetUserID() . ', partner_wydanie = ' . INSTALLATION . ' WHERE id=' . intval($voucher['id']) . ' AND serial = "' . $post['voucherserial'] . '"';
            $this->sdb->query($sql);
            if ($vtype == 'zestaw') {
                $sql_update = 'Update ' . TABLE_PREFIX . 'pakiety_uzyte SET zrealizowany = 1, data_realizacji = "' . date('Y-m-d H:i:s') . '" '
                    . 'WHERE voucherserial ="' . $post['voucherserial'] . '" AND id_item=' . intval($voucher['id']) . ' AND ptype = "vouchery"';
                $this->sdb->query($sql_update);
            }
        }

        if (is_array($wydaj['produkty_wirtualne'])) {
            $voucher = $wydaj['produkty_wirtualne'];
            $sql = 'update ' . TABLE_PREFIX . 'vprodukty SET voucher_wydany = 1, data_wydania = "' . date('Y-m-d H:i:s') . '", user_wydanie=' . User::GetUserID() . ', partner_wydanie = ' . INSTALLATION . ' WHERE id=' . intval($voucher['id']) . ' AND voucherserial = "' . $post['voucherserial'] . '"';
            $this->sdb->query($sql);
            //TODO - nie wiem co z tym zrobić?
            //$sql = 'update '.TABLE_PREFIX.'vprodukty SET voucher_wydany = 1, data_wydania = "'.date('Y-m-d H:i:s').'" WHERE id='.intval($voucher['id']).' AND serial = "'.$post['voucherserial'].'"';
            //$this->sdb->query($sql);

            if ($vtype == 'zestaw') {
                $sql_update = 'Update ' . TABLE_PREFIX . 'pakiety_uzyte SET zrealizowany = 1, data_realizacji = "' . date('Y-m-d H:i:s') . '" '
                    . 'WHERE voucherserial ="' . $post['voucherserial'] . '" AND id_item=' . intval($voucher['id']) . ' AND ptype = "produkty_wirtualne"';

                $this->sdb->query($sql_update);
            }
        }


        $response = array("result" => true, "dump" => $post, "sql" => $sql);

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function AjaxAddToBasket()
    {
        $mass_topup = false;
        if ($this->post['mass_topup']) {
            $data = json_decode($this->post['data'], true);
            $loadeddCards = Tools::GetCleanedCardsFromFile('mass_topup_cards');
            if (empty($loadeddCards)) {
                $response['errors'][] = 'Nie udało się wczytać pliku';
                $this->app->ADD('response', json_encode($response));
                $this->app->SetDecorator('Ajax');
                return;
            }
            $mass_topup = true;
        } else {
            $data = $this->post['data'];
        }

        $basket = new BasketData();
//        $basket->devDontSetBasket(); ################################################## <- DEL
        $basket->getBasket();
        //  Ustalanie danych zamówienia
        $basket->setPType($data['p_type']);
        $basket->setParentId($data['parentid']);
        $karta_src = $data['karta'];
        $quantity = isset($data['karnet']['quantity']) ? (int)$data['karnet']['quantity'] : 1;
    
        switch ($data['p_type']) {
            case 'karnet':

                for ($i = 0; $i < $quantity; $i++) {
                    if ($mass_topup) {
                        $karta = Karta::CheckCard($loadeddCards[$i], true, false, false, true);
                        $data['karta']['identyfikator'] = $karta['data']['id'];
                        $data['karta']['source'] = 'base';
                        $data['karnet']['quantity'] = 1;
                    }

                    //  Dodawanie nowego zamówienia
                    $basket->setProductToAdd('order');
                    $basket->addToBasket();
                    //  Dodawanie karnetu
                    $basket->setTicket($data['karnet']);
                    $basket->addProductPrice();
                    $basket->addProductDates();
                    $basket->setProductToAdd('ticket');
                    $basket->addToBasket();
                    //  Dodawanie karty
                    $data['karta']['id_product_karnet'] = $basket->ticket['id_product'];
                    $basket->setCard($data['karta']);
                    $basket->setProductToAdd('card');
                    $basket->addToBasket();
                    $basket->addTicketCard();
                    //  Ustalanie ubezpieczenia
                    $basket->setProductToAdd('insurance');
                    $basket->addToBasket();
                    //  Ustalanie danych ubezpieczenia w karnecie
                    $basket->setIdProduct($basket->ticket['id_product']);
                    $basket->addTicketInsurance();
                    //  Ustalanie zwrotu gwarantowanego
                    $basket->addTicketReturn();
                }   
                break;

            case 'voucher':
                $voucher = $data['voucher'];
//                $voucher['identyfikator'] = $data['identyfikator'];
//                die(var_dump($voucher));
                $basket->setProductToAdd('order');
                $basket->addToBasket();
                $basket->setVoucher($voucher);
                $basket->addProductPrice();
                $basket->addProductDates();
                $basket->setProductToAdd('voucher');
                $basket->addToBasket();
                break;

            case 'vgroup':
//                $voucher = $data['products'];
//                $voucher['identyfikator'] = $data['identyfikator'];
//                die(var_dump($voucher));
                $basket->setProductToAdd('order');
                $basket->addToBasket();
                $basket->setProductToAdd('vgroup');
                $gp = Tools::getGrupyProduktowe($data['parentid']);
                $basket->grupaProduktowa = array(
                    'grupa' => $data['grupa_produktowa'],
                    'useremail' => $data['user_email'],
                    'parentid' => $data['parentid'],
                    'dataprzyjazdu' => $data['data_przyjazdu'],
                    'nazwa' => $gp[$data['grupa_produktowa']]['nazwa']
                );
                $basket->addToBasket();
                foreach ($data['products'] as $voucher) {
                    $basket->setVoucher($voucher);
                    $basket->addProductPrice();
                    $basket->addProductDates();
                    $basket->setProductToAdd('voucher');
                    $basket->addToBasket();
                }
                break;

            case 'ubezpieczenie':
                $voucher = $data['ubezpieczenie'];
                $basket->setProductToAdd('order');
                $basket->addToBasket();
                $basket->setInsurance($voucher);
                $basket->addProductPrice();
                $basket->addProductDates();
                $basket->setProductToAdd('insurance');
                $basket->dev['dont_set_basket'] = 1;
                $basket->addToBasket();
                $idp = $basket->insurance['id_product'];
                foreach ($basket->basket['zamowienia'] as $k => $zam) {
                    if ($zam['id_basket'] == $basket->id_basket) {
                        foreach ($zam['ubezpieczenie'] as $ik => $ubez) {
                            if ($ubez['id_product'] == $idp) {
                                $basket->basket['zamowienia'][$k]['p_conf']['set']['type'] = 1;
                                $basket->basket['zamowienia'][$k]['p_conf']['all'] = 1;
                                $basket->basket['zamowienia'][$k]['ubezpieczenie'][$ik]['source'] = 'standalone';
                            }
                        }
                    }
                }
                unset($basket->dev['dont_set_basket']);
                $basket->setBasket();
                break;
        }


//        $basket->db(true); ########################################################################
        if ($basket->result) {
            $response['ok'] = true;
            $response['quantity'] = $quantity;
            $response['basket_result'] = $basket->result;
        } else {
            $response['errors'] = $basket->errors;
            $response['errors'][] = 'Nie udało się dodać produktu do koszyka!';
        }

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function ajaxGetBasketCont()
    {
        $basket = new BasketData();
        $basket->getBasket();
        $basket->getWidgetData();
        $params['widget'] = $basket->widget;
        file_put_contents('_widget.dat', var_export($basket->widget, true));
        $this->app->SetDecorator('Ajax');
        $this->app->AddDecoratorData('params', $params);
        $this->app->SetTemplate('widgets/pbasket-container.tpl');
        $resp = new DecoratorSmarty($this->app);
        echo $resp->output;
    }

    public function AjaxDelFromBasket()
    {
        $basket = new BasketData();
        $basket->GetBasket();
        $basket->SetIdBasket($this->post['id_basket']);
        $basket->DeleteOrder();

        if ($basket->result) {
            $response['deleted'] = true;
        } else {
            $response['errors'][] = 'Nie udało się usunąć produktu z koszyka';
        }

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    // PARTNER ORDER DETAILS
    public function AjaxOrderDetails()
    {
    
        $settings = array('set_resorts_string' => true);
        $up = new UserProductsData();
        $up->partnertype = intval($this->partner['partner']['partnertype']);
        $up->setProductId($this->post['order_id']);
        $up->setProductType('zamowienia');
        $up->setFormatSettings($settings);
        $up->getProduct();


          // Add ZIP download capability if there are vouchers
          if (!empty($up->output['voucher_list'])) {
            $up->output = $this->createVoucherZip($up->output);
        }


        $this->app->SetDecorator('Ajax');
        $this->app->AddDecoratorData('data', $up->rOutput());
        $this->app->SetTemplate('modules/partner/order-details.tpl');
        $resp = new DecoratorSmarty($this->app);
        echo $resp->output;
    }

    /**
     * Helper method to create ZIP file for vouchers
     * 
     * @param array $orderData Order data containing voucher_list and id
     * @return array Modified order data with zip_download information
     */
    private function createVoucherZip($orderData)
    {
        if (empty($orderData['voucher_list'])) {
            return $orderData;
        }

        // Get the directory path from the first PDF file
        $firstPdf = reset($orderData['voucher_list']);
        $pdfDirectory = dirname($firstPdf['pdf']);
        
        $zipFileName = 'order_' . $orderData['id'] . '_vouchers.zip';
        $zipPath = $pdfDirectory . '/' . $zipFileName;
        
        // Create new ZIP archive
        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
            // Add PDFs to ZIP
            foreach ($orderData['voucher_list'] as $voucher) {
                if (!empty($voucher['pdf']) && file_exists($voucher['pdf'])) {
                    $fileName = basename($voucher['pdf']);  // Use original PDF filename
                    $zip->addFile($voucher['pdf'], $fileName);
                }
            }
            $zip->close();
            
            // Create proper download URL
            // Assuming PDFs are in a web-accessible directory
            $downloadPath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $zipPath);
            $downloadUrl = SITE_URL . ltrim($downloadPath, '/');
            
            // Add ZIP info to order data
            $orderData['zip_download'] = [
                'href' => $downloadUrl,
                'filename' => $zipFileName
            ];
        }

        return $orderData;
    }


    public function AjaxCardDetails()
    {
        $post = $this->app->postparam;

        if ($post['card_id']) {
            $fields = array(
                'id',
                'serial',
                'typ_karty',
                'id_oferty',
                'id_produktu_oferta',
                'id_zamowienia',
                'producent_karty',
                'dostawca_systemu',
                'status',
                'data_wysylki',
                'opis_karty',
                'img_path',
            );

            $up = new UserProductsData();
            $up->SetCardId($post['card_id']);
            $up->SetCardFields($fields);
            $up->AddCardName();
            $up->AddCardTypeDisp();
            $up->AddCardTickets();
            $up->AddTicketResorts();
            $up->AddTicketName();
            $up->AddTicketTime();
            $up->AddTicketDaysLeft();
            $up->SetTicketResortsString();
            $up->GetTicketMongoData();
            $up->GetCards();
            $up->SetSingle();

            if ($up->result) {
                $response['card_data'] = $up->output;
            } else {
                $response['errors'][] = 'Nie udało się pobrać szczegółów karty';
            }
        } else {
            $response['errors'][] = 'Brak identyfikatora karty';
        }

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function AjaxCheckMassTopUpCards()
    {
        $post = $this->app->postparam;
        $files = $_FILES;
        $response = array();
        
        $expected_quantity = (int)$post['expected_cards_quantity'];
        if ($files['mass_topup_cards']['error'] != 0 || $files['mass_topup_cards']['size'] == 0) {
            $response['match'] = false;
            $response['error'] = 'Nie udało się wczytać pliku';
            $this->app->ADD('response', json_encode($response));
            $this->app->SetDecorator('Ajax');
            return;
        }
        
        $tmp_file = $files['mass_topup_cards']['tmp_name'];
        $csv = file($tmp_file);
        $actual_count = 0;
        $response['error'] = '';
        if (count($csv) > 0) {
            foreach ($csv as $i => $c) {
                $cvalue = trim(rtrim($c,", \n\r\t\v"));
                if (!empty($cvalue)) {
                    $actual_count++;
                    $csv[$i] = $cvalue;
                } else {
                    unset($csv[$i]);
                }
            }
        }
        $response['match'] = ($actual_count === $expected_quantity);
        if ($response['match']) {
            foreach ($csv as $card) {
                $check = Karta::CheckCard($card, false, false, false, true);
                if ($check['source'] === 'main' && $check['error'] !== '') {
                    $response['error'] .= "Karta " . $card . ": " . $check['error'] . "\n";
                };
                if ($check['source'] === 'tmp' && $check['cid'] === false) {
                    $response['error'] .= "Karta " . $card . ": Brak karty w systemie tmp\n";
                }
                if ($check['source'] === 'none' && $check['cid'] === false) {
                    $response['error'] .= "Karta " . $card . ": " . ($check['error'] ?? "Brak karty w systemie\n");
                }
            }
            $response['match'] = empty($response['error']);
        } else {
            $response['error'] = "Liczba kart w pliku ($actual_count) nie zgadza się z oczekiwaną ilością ($expected_quantity)";
        }
        
        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Strony w panelu partnera
    ////////////////////////////////////////////////////////////////////////////////

    /**
     * Dane konta
     */
    public function MyData()
    {
        if ($this->app->postparam) {
            $this->tpl_data['postback'] = $this->app->postparam;
        } else {
            $this->tpl_data['postback']['action'] = 'invoice';
        }

        $userdata = User::GetUserData();

        $base = array(
            'email' => $userdata['base']['email'],
            'imie' => $userdata['base']['imie'],
            'nazwisko' => $userdata['base']['nazwisko'],
            'data_rejestracji' => date('Y-m-d H:i:s', $userdata['base']['registerdate']),
            'defaultlang' => $userdata['base']['defaultlang']
        );


        $this->tpl_data['partner'] = $this->partner;
        $this->tpl_data['base_data'] = $base;
        $this->tpl_data['pin_set'] = Auth::PinSet();
        $this->Display('mydata');
    }

    public function Chckinsurance()
    {
        $this->tpl_data['partner'] = $this->partner;
        $this->tpl_data['base_data'] = $base;
        $this->tpl_data['pin_set'] = Auth::PinSet();
        $this->Display('chckinsurance');
    }

    public function ChangeMyData()
    {
        $post = $this->app->postparam;

        switch ($post['action']) {
            case 'invoice':
                foreach ($post as &$p) {
                    $p = trim($p);
                }

                $data = array(
                    array('name', $_POST['name'], 'STRING'),
                    array('shortname', $_POST['shortname'], 'STRING'),
                    array('city', $_POST['city'], 'STRING'),
                    array('postalcode', $_POST['postalcode'], 'STRING'),
                    array('address', $_POST['address'], 'STRING'),
                    array('nip', $_POST['nip'], 'STRING'),
                    array('bankname', $_POST['bankname'], 'STRING'),
                    array('bankaccount', $_POST['bankaccount'], 'STRING')
                );
                $where = array(
                    array('id', $this->partner['partner']['id'], 'INT'),
                );

                if (false === $this->sdb->update_rows('partners', $data, $where)) {
                    $this->alerts['danger'][] = 'Nie udało się zaktualizować danych';
                    break;
                }
                $this->alerts['success'][] = 'Dane zostały zaktualizowane';
                break;

            case 'account':

                foreach ($post as &$p) {
                    $p = trim($p);
                }

                $val[] = array($post['imie'], 'NAME', 'Imię');
                $val[] = array($post['nazwisko'], 'NAME', 'Nazwisko');

                if ($post['email'] != User::GetUserEmail()) {
                    $val[] = array($post['email'], 'EMAIL', 'Email');
                    $val[] = array($post['email'], 'EMAILUSED');
                }

                $val = Validation::ValForm($val);


                $data = array(
                    array('imie', $post['imie'], 'STRING'),
                    array('nazwisko', $post['nazwisko'], 'STRING'),
                    array('email', $post['email'], 'STRING'),
                    array('defaultlang', $post['jezyk'], 'INT')
                );
                $where = array(
                    array('id', User::GetUserID(), 'INT')
                );

                if (false === $this->sdb->update_rows('uzytkownik', $data, $where)) {
                    $this->alerts['danger'][] = 'Nie udało się zaktualizować danych';
                    break;
                }
                $this->alerts['success'][] = 'Dane zostały zaktualizowane';
                $_SESSION['LANG'] = $post['jezyk'];
                break;

            case 'password':
                $val[] = array(array($post['nowe_haslo'], 100), 'MAX_LENGTH', 'Nowe hasło');
                $val[] = array(array($post['nowe_haslo'], 6), 'MIN_LENGTH', 'Nowe hasło');
                $val[] = array($post['haslo'], 'CONFPASS');
                $val[] = array(
                    array($post['nowe_haslo'], $post['powt_nowe_haslo']),
                    'MATCHES',
                    array('Nowe hasło', 'Powtórz nowe hasło')
                );

                $val = Validation::ValForm($val);

                if ($val['errors']) {
                    $this->alerts['danger'] = $val['warnings'];

                } elseif (User::ChangeUserData($post)) {
                    $this->alerts['success'][] = 'Dane zostały zaktualizowane';
                } else {
                    $this->alerts['danger'][] = 'Nie udało się zaktualizować danych';
                }

                break;

            case 'pin':
                $val = Auth::RegisterPinVaildate($post);

                if ($val['errors']) {
                    $this->alerts['danger'] = $val['warnings'];
                } elseif (Auth::RegisterPin($post['nowy_pin'])) {
                    $this->alerts['success'][] = 'Dane zostały zaktualizowane';
                } else {
                    $this->alerts['danger'][] = 'Nie udało się zaktualizować danych';
                }

                break;
        }

        header('Location:' . SITE_URL . 'partner.php?partner&a=mydata');
        die();
    }

    public function Contact()
    {
        if (intval($_GET['send']) == 1) {
            $post = $_POST;
            $recipient = '<EMAIL>';
            $subject = 'Wiadmość od partnera: ' . $this->partner['partner']['shortname'] . ', ID: ' . $this->partner['partner']['id'] . PHP_EOL;
            $msg = "Partner: " . $this->partner['partner']['shortname'] . PHP_EOL;
            $msg .= 'ID: ' . $this->partner['partner']['id'] . PHP_EOL;
            $msg .= 'User: ' . User::GetUserFullName() . PHP_EOL;
            $msg .= 'User email: ' . User::GetUserEmail() . PHP_EOL;
            $msg .= 'User ID: ' . User::GetUserID() . PHP_EOL;
            $msg .= 'Date: ' . date('Y-m-d H:i:s') . PHP_EOL;
            $msg .= 'Form subject : -------------------------' . PHP_EOL;
            $msg .= $post['subject'] . PHP_EOL;
            $msg .= 'Form message : -------------------------' . PHP_EOL;
            $msg .= $post['message'] . PHP_EOL;
            // wysłać meila
//            $resp['msg'] = $msg;
            $resp['status'] = 'true';
            echo json_encode($resp);
            die();
        }

        $this->Display('kontakt');
    }

    /**
     * Generuje stronę początkową
     */
    public function Home()
    {
        switch ($this->partner['partner']['partnertype']) {
            case 2:
                $dat = new DateTime(date('Y-m-d') . ' 00:00:01');
                $dat->sub(new DateInterval('P6M'));
                $dataod = strtotime($dat->format('Y-m-d H:i:s'));
                $sql = "SELECT ROUND(SUM(brutto)/100,2) as brutto, DATE_FORMAT(ts,'%Y-%m-%d') as pdata "
                    . "FROM ts_zamowienia_koszty WHERE id_platnika = " . INSTALLATION . " AND data_platnosci > " . $dataod . " group by FROM_UNIXTIME(data_platnosci,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wdata = array();
                while ($r = $this->sdb->fetch_row()) {
                    $wdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $r['brutto'] . '}';
                }
                $this->pfooter_data['wykres'] = 'os';
                $this->pfooter_data['wykres_data'] = join(',' . PHP_EOL, $wdata);

                $pdm = date('Y-m') . '-01 00:00:01';
                $sqlz = 'SELECT ROUND(SUM(brutto)/100,2) as brutto FROM ' . TABLE_PREFIX . 'zamowienia_koszty WHERE id_platnika = ' . INSTALLATION . ' AND data_platnosci > ' . strtotime($pdm);
                $res = $this->sdb->select_r($sqlz);
                $this->tpl_data['wartosc_zamowien'] = $res['brutto'];
                $sqlc = 'SELECT count(DISTINCT id_zamowienia) as id FROM ' . TABLE_PREFIX . 'zamowienia_koszty WHERE id_platnika = ' . INSTALLATION . ' AND data_platnosci > ' . strtotime($pdm);
                $res = $this->sdb->select_r($sqlc);
//
//                $invoicesSQL = 'SELECT * FROM '.TABLE_PREFIX.'faktury inv LEFT JOIN '.TABLE_PREFIX.'partners_meta meta ON inv.installation = meta.id_parent WHERE meta.id_parent = '.$this->partner['partner']['id'];
//                $this->tpl_data['faktury'] = $this->sdb->select($invoicesSQL);

                $this->tpl_data['ilosc_zamowien'] = $res['id'];
                $tpl = 'home';
                break;
            case 1:
                $dat = new DateTime(date('Y-m-d') . ' 00:00:01');
                $dat->sub(new DateInterval('P6M'));
                $dataod = strtotime($dat->format('Y-m-d H:i:s'));
                $sql = "SELECT ROUND(SUM(brutto)/100,2) as brutto, DATE_FORMAT(ts,'%Y-%m-%d') as pdata "
                    . "FROM ts_zamowienia_koszty WHERE id_platnika = " . INSTALLATION . " AND data_platnosci > " . $dataod . " group by FROM_UNIXTIME(data_platnosci,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
//                $stanKonta = 'SELECT sum(kwota) as stan FROM '.TABLE_PREFIX.'portfel WHERE partner='.INSTALLATION;
                $wdata = array();
                while ($r = $this->sdb->fetch_row()) {
                    $wdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $r['brutto'] . '}';
                }
                $this->pfooter_data['wykres'] = 'sp';
                $this->pfooter_data['wykres_data'] = join(',' . PHP_EOL, $wdata);
                $sql_s = 'SELECT round(sum(kwota)/100,2) as start FROM ' . TABLE_PREFIX . 'portfel WHERE partner = ' . INSTALLATION . ' AND data < ' . $dataod;
                $start = $this->sdb->select_r($sql_s);
                $start = $start['start'];
                $sql = "SELECT ROUND(SUM(kwota)/100,2) as brutto, FROM_UNIXTIME(data,'%Y-%m-%d') as pdata "
                    . "FROM ts_portfel WHERE partner = " . INSTALLATION . " AND data > " . $dataod . " group by FROM_UNIXTIME(data,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wpdata = array();

                while ($r = $this->sdb->fetch_row()) {
//                    $val = $r['kwota'] < 0 ? $start - $r['kwota'] : $rkwota;
                    $start += $r['brutto'];
                    $wpdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $start . '}';
                }
                $this->pfooter_data['wpwykres_data'] = join(',' . PHP_EOL, $wpdata);
                $pdm = date('Y-m') . '-01 00:00:01';
                $sqlc = 'SELECT count(DISTINCT id_zamowienia) as id FROM ' . TABLE_PREFIX . 'zamowienia_koszty WHERE id_platnika = ' . INSTALLATION . ' AND data_platnosci > ' . strtotime($pdm);
                $res = $this->sdb->select_r($sqlc);
                $this->tpl_data['ilosc_zamowien'] = $res['id'];

                $wallet_balance = Wallet::StaticCheckBalance(INSTALLATION);
                $wallet_balance = Tools::CC($wallet_balance / 100, true);
                $up = new UserProductsData();
                $up->SetUserId();
                $up->GetCardsAmount();
                $voucher = Voucher::where('installation', INSTALLATION)->findOne();
                $card_quantity = $up->output['card_quantity'];
                $wallet_history = Wallet::WalletFullHistory('data', 'DESC', 5);
                $this->tpl_data['voucher_exist'] = $voucher;
                $this->tpl_data['card_quantity'] = $card_quantity;
                $this->tpl_data['wallet_history'] = $wallet_history;
                $this->tpl_data['wallet_balance'] = $wallet_balance;
                $tpl = 'home_1';
                break;

            case 6:
                $dat = new DateTime(date('Y-m-d') . ' 00:00:01');
                $dat->sub(new DateInterval('P6M'));
                $dataod = strtotime($dat->format('Y-m-d H:i:s'));
                $sql = "SELECT ROUND(SUM(brutto)/100,2) as brutto, DATE_FORMAT(ts,'%Y-%m-%d') as pdata "
                    . "FROM ts_zamowienia_koszty WHERE id_platnika = " . INSTALLATION . " AND data_platnosci > " . $dataod . " group by FROM_UNIXTIME(data_platnosci,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wdata = array();
                while ($r = $this->sdb->fetch_row()) {
                    $wdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $r['brutto'] . '}';
                }
                $this->pfooter_data['wykres'] = 'sp';
                $this->pfooter_data['wykres_data'] = join(',' . PHP_EOL, $wdata);
                $sql_s = 'SELECT round(sum(kwota)/100,2) as start FROM ' . TABLE_PREFIX . 'portfel WHERE partner = ' . INSTALLATION . ' AND data < ' . $dataod;
                $start = $this->sdb->select_r($sql_s);
                $start = $start['start'];
                $sql = "SELECT ROUND(SUM(kwota)/100,2) as brutto, FROM_UNIXTIME(data,'%Y-%m-%d') as pdata "
                    . "FROM ts_portfel WHERE partner = " . INSTALLATION . " AND data > " . $dataod . " group by FROM_UNIXTIME(data,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wpdata = array();

                while ($r = $this->sdb->fetch_row()) {
//                    $val = $r['kwota'] < 0 ? $start - $r['kwota'] : $rkwota;
                    $start += $r['brutto'];
                    $wpdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $start . '}';
                }
                $this->pfooter_data['wpwykres_data'] = join(',' . PHP_EOL, $wpdata);
                $pdm = date('Y-m') . '-01 00:00:01';
                $sqlc = 'SELECT count(DISTINCT id_zamowienia) as id FROM ' . TABLE_PREFIX . 'zamowienia_koszty WHERE id_platnika = ' . INSTALLATION . ' AND data_platnosci > ' . strtotime($pdm);
                $res = $this->sdb->select_r($sqlc);
                $this->tpl_data['ilosc_zamowien'] = $res['id'];

                $wallet_balance = Wallet::StaticCheckBalance(INSTALLATION);
                $wallet_balance = Tools::CC($wallet_balance / 100, true);
                $up = new UserProductsData();
                $up->SetUserId();
                $up->GetCardsAmount();
                $card_quantity = $up->output['card_quantity'];
                $wallet_history = Wallet::WalletFullHistory('data', 'DESC', 5);
                $this->tpl_data['card_quantity'] = $card_quantity;
                $this->tpl_data['wallet_history'] = $wallet_history;
                $this->tpl_data['wallet_balance'] = $wallet_balance;
                $tpl = 'home_6';
                break;

            case 3:
                $dat = new DateTime(date('Y-m-d') . ' 00:00:01');
                $dat->sub(new DateInterval('P6M'));
                $dataod = strtotime($dat->format('Y-m-d H:i:s'));
                $sql = "SELECT ROUND(SUM(brutto)/100,2) as brutto, DATE_FORMAT(ts,'%Y-%m-%d') as pdata "
                    . "FROM ts_zamowienia_koszty WHERE id_platnika = " . INSTALLATION . " AND data_platnosci > " . $dataod . " group by FROM_UNIXTIME(data_platnosci,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wdata = array();
                while ($r = $this->sdb->fetch_row()) {
                    $wdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $r['brutto'] . '}';
                }
                $this->pfooter_data['wykres'] = 'sp';
                $this->pfooter_data['wykres_data'] = join(',' . PHP_EOL, $wdata);
                $sql_s = 'SELECT round(sum(kwota)/100,2) as start FROM ' . TABLE_PREFIX . 'portfel WHERE partner = ' . INSTALLATION . ' AND data < ' . $dataod;
                $start = $this->sdb->select_r($sql_s);
                $start = $start['start'];
                $sql = "SELECT ROUND(SUM(kwota)/100,2) as brutto, FROM_UNIXTIME(data,'%Y-%m-%d') as pdata "
                    . "FROM ts_portfel WHERE partner = " . INSTALLATION . " AND data > " . $dataod . " group by FROM_UNIXTIME(data,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wpdata = array();

                while ($r = $this->sdb->fetch_row()) {
//                    $val = $r['kwota'] < 0 ? $start - $r['kwota'] : $rkwota;
                    $start += $r['brutto'];
                    $wpdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $start . '}';
                }
                $this->pfooter_data['wpwykres_data'] = join(',' . PHP_EOL, $wpdata);
                $pdm = date('Y-m') . '-01 00:00:01';
                $sqlc = 'SELECT count(DISTINCT id_zamowienia) as id FROM ' . TABLE_PREFIX . 'zamowienia_koszty WHERE id_platnika = ' . INSTALLATION . ' AND data_platnosci > ' . strtotime($pdm);
                $res = $this->sdb->select_r($sqlc);
                $this->tpl_data['ilosc_zamowien'] = $res['id'];

                $wallet_balance = Wallet::StaticCheckBalance(INSTALLATION);
                $wallet_balance = Tools::CC($wallet_balance / 100, true);
                $up = new UserProductsData();
                $up->SetUserId();
                $up->GetCardsAmount();
                $card_quantity = $up->output['card_quantity'];
                $wallet_history = Wallet::WalletFullHistory('data', 'DESC', 5);
                $this->tpl_data['card_quantity'] = $card_quantity;
                $this->tpl_data['wallet_history'] = $wallet_history;
                $this->tpl_data['wallet_balance'] = $wallet_balance;
                $tpl = 'home_6';
                break;

            case 9:
                $dat = new DateTime(date('Y-m-d') . ' 00:00:01');
                $dat->sub(new DateInterval('P6M'));
                $dataod = strtotime($dat->format('Y-m-d H:i:s'));
                $sql = "SELECT ROUND(SUM(brutto)/100,2) as brutto, DATE_FORMAT(ts,'%Y-%m-%d') as pdata "
                    . "FROM ts_zamowienia_koszty WHERE id_platnika = " . INSTALLATION . " AND data_platnosci > " . $dataod . " group by FROM_UNIXTIME(data_platnosci,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wdata = array();
                while ($r = $this->sdb->fetch_row()) {
                    $wdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $r['brutto'] . '}';
                }
                $this->pfooter_data['wykres'] = 'os';
                $this->pfooter_data['wykres_data'] = join(',' . PHP_EOL, $wdata);

                $pdm = date('Y-m') . '-01 00:00:01';
                $sqlz = 'SELECT ROUND(SUM(brutto)/100,2) as brutto FROM ' . TABLE_PREFIX . 'zamowienia_koszty WHERE id_platnika = ' . INSTALLATION . ' AND data_platnosci > ' . strtotime($pdm);
                $res = $this->sdb->select_r($sqlz);
                $this->tpl_data['wartosc_zamowien'] = $res['brutto'];
                $sqlc = 'SELECT count(DISTINCT id_zamowienia) as id FROM ' . TABLE_PREFIX . 'zamowienia_koszty WHERE id_platnika = ' . INSTALLATION . ' AND data_platnosci > ' . strtotime($pdm);
                $res = $this->sdb->select_r($sqlc);
                $this->tpl_data['ilosc_zamowien'] = $res['id'];
                $tpl = 'home';
                break;
        }

        $notes_count = Sms::NotesCount();
        $this->tpl_data['ptype'] = $this->partner['partner']['partnertype'];
        $this->tpl_data['notes_count'] = $notes_count;
        $this->Display($tpl);
    }

    /**
     * Lista kart
     */
    public function Cards()
    {
        $post = $this->app->postparam;


        $fields = array('id', 'serial', 'opis_karty', 'img_path');

        $up = new UserProductsData();
        $up->SetCardFields($fields);
        $up->SetUserId();

        if ($post['serial']) {
            $up->SetCardSerial($post['serial']);
        }

        $up->GetCardActive();
        $up->GetCardTicketsActiveFlag();
        $up->GetCards();

        if (!$up->result) {
            $this->alerts['warning'][] = 'Podczas pobierania danych wystąpił błąd';
        }

        $this->tpl_data['cards'] = $up->output;

        $this->Display('cards');
    }

    public function Raports()
    {

        switch ($this->partner['partner']['partnertype']) {

            //osrodek
            case 2:
                include('application/admin_v4_0/modules/finance/reportsOsrodek.php');
                $mid = INSTALLATION;
                $dataod = false;
                $datado = false;
                if ($this->app->urlparam['dataod'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['dataod'])) {
                        $postback['dataod'] = $dataod;
                    }
                }
                if ($this->app->urlparam['datado'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['datado'])) {
                        $datado = $this->app->urlparam['datado'];
                        $postback['datado'] = $datado;
                    }
                } else {
                    $postback['datado'] = $datado = date('Y-m-d', time());
                    $postback['dataod'] = $dataod = DateTime::createFromFormat('Y-m-d', $datado)->sub(new DateInterval('P30D'))->format('Y-m-d');
                }
                $raport = new Reports4Osrodek($mid, $dataod, $datado);
                $raport->PrepareReport();
                $postback['produkty'] = $raport->produkty;
                $postback['kupony'] = $raport->kupony;
                $postback['zwroty'] = $raport->zwroty;
                $postback['liczba_zamowien'] = $raport->OrdersCount();
                $postback['prowizja_eskipass'] = $raport->suma_prowizja_eskipass;
                $postback['sprzedazwp'] = $raport->sprzedazWP;
                $postback['suma_do_zaplaty'] = $raport->suma_do_zaplaty;
                $this->app->ADD('raport', 'osrodek');
                $params['partner'] = $mid;
                $po = Tools::GetPartners(2, true);
                $params['ofertanazwa'] = $po[$mid]['shortname'];
                $postback['prowizje'] = $raport->provisionHistory();
                //            $postback['zestawy'] = $raport->zestawy;
                $postback['income'] = $raport->income;
                $this->app->ADD('rozliczenia', $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia WHERE idpartnera =' . $mid));
                $partnerzysp = Tools::GetPartners(1);
                $partnerzyfr = Tools::GetPartners(6);
                $offp = self::getPSPList();
                foreach ($partnerzysp as $v) {
                    if (in_array($v['id'], $offp)) {
                        $ppartners[] = $v;
                        $sql = 'SELECT count(k.id) as ile FROM ' . TABLE_PREFIX . 'karty k where k.id_usera in (SELECT iduser FROM ' . TABLE_PREFIX . 'partner_user WHERE idpartner = ' . $v['id'] . ')';
                        $ile = $this->sdb->select_r($sql);
                        $v['ile_kart'] = intval($ile['ile']);
                        $v['saldo'] = Wallet::StaticCheckBalance($v['id'], true);
                        $sdo = '';
                        $sod = '';
                        if ($dataod) {
                            $dataodts = strtotime($dataod);
                            $sod = ' AND data > ' . $dataodts;
                        }
                        if ($datado) {
                            $datadots = strtotime($datado);
                            $sdo = ' AND data < ' . $datadots;
                        }
                        $sumdo = $this->sdb->select_r('SELECT round(sum(kwota)/100,2) as sumdo FROM ' . TABLE_PREFIX . 'portfel WHERE kwota > 0 AND partner = ' . $v['id'] . $sdo . $sod);
                        $v['sumdo'] = is_null($sumdo['sumdo']) ? 0 : $sumdo['sumdo'];
                        $lastro = $this->sdb->select_r('SELECT round(brutto/100,2) as kwota, datado FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE idpartnera = ' . $v['id'] . ' ORDER BY datado DESC LIMIT 1');
                        if ($lastro['datado'] > 0) {
                            $v['rozlastsaldo'] = $lastro['kwota'];
                            $v['rozlastdata'] = date('Y-m-d', $lastro['datado']);
                            $v['rozcdata'] = date('Y-m-d', $lastro['datado'] + 2) . ' - ' . date('Y-m-d', strtotime(' - 1 day'));
                            $now = time();
                            $sql = 'SELECT round(sum(zp.brutto/100),2) as brutto FROM ' . TABLE_PREFIX . 'zamowienia_przychod zp '
                                . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia z ON z.id = zp.id_zamowienia '
                                . 'LEFT JOIN ' . TABLE_PREFIX . 'platnosci p ON z.id = p.item '
                                . 'WHERE p.pay_type = ' . PAYWALLET_ID . ' AND z.installation = ' . $v['id'] . ' AND p.paydate BETWEEN ' . $lastro['datado'] . ' AND ' . $now;
                            $rozcsaldo = $this->sdb->select_r($sql);
                            $v['rozcsaldo'] = round(floatval($rozcsaldo['brutto']), 2);
                        } else {
                            $v['rozlastsaldo'] = 0;
                            $v['rozlastdata'] = 'brak';
                            $v['rozcdata'] = 'do ' . date('Y-m-d', strtotime(' - 1 day'));
                            $now = time();
//                            $now = strtotime(date('Y-m-d',strtotime(' - 1 day')).' 23:59:59');
                            $sql = 'SELECT round(sum(zp.brutto/100),2) as brutto FROM ' . TABLE_PREFIX . 'zamowienia_przychod zp '
                                . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia z ON z.id = zp.id_zamowienia '
                                . 'LEFT JOIN ' . TABLE_PREFIX . 'platnosci p ON z.id = p.item '
                                . 'WHERE p.pay_type = ' . PAYWALLET_ID . ' AND z.installation = ' . $v['id'] . ' AND paydate BETWEEN 1 AND ' . $now;
                            $rozcsaldo = $this->sdb->select_r($sql);
                            $v['rozcsaldo'] = round(floatval($rozcsaldo['brutto']), 2);
                        }
                        $ofpartners[] = $v;
                    }
                }
                foreach ($partnerzyfr as $v) {
                    if (in_array($v['id'], $offp)) {
                        $ppartners[] = $v;
                    }
                }

                $this->app->ADD('partnerzysp', $ppartners);
                $this->app->ADD('ofpartners', $ofpartners);
                $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_osrodek.tpl");
                $this->app->ADD('postback', $postback);
                $params['dataod'] = $dataod;
                $params['datado'] = $datado;
                $params['partnerdata'] = $this->partner;
                $this->app->ADD('params', $params);
                break;
            //ubezpieczyciel
            case 3:
                include('application/admin_v4_0/modules/finance/reportsUbezpieczenie.php');
                $mid = INSTALLATION;
                $dataod = false;
                $datado = false;
                if ($this->app->urlparam['dataod'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['dataod'])) {
                        $dataod = $this->app->urlparam['dataod'];
                        $postback['dataod'] = $dataod;
                    }
                }
                if ($this->app->postparam['datado'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['datado'])) {
                        $datado = $this->app->urlparam['datado'];
                        $postback['datado'] = $datado;
                    }
                } else {

                    $postback['datado'] = $datado = date('Y-m-d', time());
                }
                $raport = new Reports4Ubezpieczenie($mid, $dataod, $datado);
                $raport->PrepareReport();
                $postback['produkty'] = $raport->produkty;
                $postback['income'] = $raport->income;
                $postback['prowizja'] = $raport->prowizja;
                $postback['kupony'] = $raport->kupony;
                $postback['liczba_zamowien'] = $raport->OrdersCount();
                $this->app->ADD('raport', 'ubezpieczenia');
                $postback['partner'] = $raport->partner;
                $this->app->ADD('rozliczenia',
                    $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia WHERE idpartnera =' . $mid . ' ORDER BY id DESC'));
                $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_ubezpieczenia.tpl");
                $this->app->ADD('postback', $postback);
                $params['dataod'] = $dataod;
                $params['datado'] = $datado;
                $this->app->ADD('params', $params);
                break;
            //partrtner franczyza
            //sprzedazowy
            case 6:
            case 1:
                include('application/admin_v4_0/modules/finance/reportsSprzedaz.php');
                $mid = INSTALLATION;
                $dataod = false;
                $datado = false;
                if ($this->app->urlparam['dataod'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['dataod'])) {
                        $dataod = $this->app->urlparam['dataod'];
                        $postback['dataod'] = $dataod;
                    }
                }
                if ($this->app->urlparam['datado'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['datado'])) {
                        $datado = $this->app->urlparam['datado'];
                        $postback['datado'] = $datado;
                    }
                } else {
                    $postback['datado'] = $datado = date('Y-m-d', time());
                }
                $raport = new Reports4Sprzedaz($mid, $dataod, $datado);
                $raport->PrepareReport();
                $postback['produkty'] = $raport->produkty;
                $postback['income'] = $raport->income;
                $postback['prowizja'] = $raport->prowizja;
                $postback['liczba_zamowien'] = $raport->OrdersCount();
                $this->app->ADD('raport', 'sprzedaz');
                $postback['partner'] = $raport->partner;
                $this->app->ADD('rozliczenia',
                    $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia WHERE idpartnera =' . $mid . ' ORDER BY id DESC'));
                $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_sprzedaz.tpl");
                $this->app->ADD('postback', $postback);
                $params = array(
                    'dataod' => $dataod,
                    'datado' => $datado
                );
                $this->app->ADD('params', $params);
                break;
            //producent kart
            case 8:

                break;
            //wirtualny produkt
            case 9:
                include('application/admin_v4_0/modules/finance/reportsVprodukt.php');
                $mid = INSTALLATION;
                $dataod = false;
                $datado = false;
                if ($this->app->urlparam['dataod'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['dataod'])) {
                        $dataod = $this->app->urlparam['dataod'];
                        $postback['dataod'] = $dataod;
                    }
                }
                if ($this->app->urlparam['datado'] != '') {
                    $reg = '/\d{4}-\d{2}-\d{2}/';
                    if (preg_match($reg, $this->app->urlparam['datado'])) {
                        $datado = $this->app->urlparam['datado'];
                        $postback['datado'] = $datado;
                    }
                } else {
                    $postback['datado'] = $datado = date('Y-m-d', time());
                }
                $raport = new Reports4Vprodukt($mid, $dataod, $datado);
                $raport->PrepareReport();
                $postback['produkty'] = $raport->produkty;
                $postback['income'] = $raport->income;
                $postback['prowizja'] = $raport->prowizja;
                $postback['kupony'] = $raport->kupony;
                $postback['zwroty'] = $raport->zwroty;
                $postback['dowyplaty'] = $raport->dowyplaty;
                $postback['liczba_zamowien'] = $raport->OrdersCount();
                $this->app->ADD('raport', 'vprodukt');
                $postback['partner'] = $raport->partner;
                $this->app->ADD('rozliczenia',
                    $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia WHERE idpartnera =' . $mid . ' ORDER BY id DESC'));
                //            $postback['sellbybasket'] = $raport->SellByBasketType();
                $params = array(
                    'dataod' => $dataod,
                    'datado' => $datado
                );
                $this->app->ADD('postback', $postback);
                $this->app->ADD('params', $params);
                $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_vprodukt.tpl");
                break;
        }
        $this->header_data['add_css'][] = '<link href="' . COMMONDIR . 'assets/css/partner_raporty.css" rel="stylesheet" media="screen">';
        $this->Display('raport');

    }


    /**
     * Zwraca tabelę zamówień w zależności od kontekstu
     * @param string $partner
     * @param array $vars
     * @param int $strcount
     * @param int $s
     * @return JSON
     */
    public function AjaxOrderList($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 2:
                include "application/admin_v4_0/modules/finance/reportsOsrodek.php";
                $data = Reports4Osrodek::tabelaZamowienia();
                break;

            case 6:
            case 1:
                include "application/admin_v4_0/modules/finance/reportsSprzedaz.php";
                $data = Reports4Sprzedaz::tabelaZamowienia();
                break;

            case 3:
                include "application/admin_v4_0/modules/finance/reportsUbezpieczenie.php";
                $data = Reports4Ubezpieczenie::tabelaZamowienia();
                break;
            case 9:
                include "application/admin_v4_0/modules/finance/reportsVprodukt.php";
                $data = Reports4Vprodukt::tabelaZamowienia();
                break;

        }
        $this->app->ADD('response', $data);
        $this->app->SetDecorator('Ajax');
//        echo $data;
//        die();
    }

    /**
     * Zwraca tabelę najlepszych kupców
     * @param string $partner
     * @param array $vars
     * @param int $strcount
     * @param int $s
     * @return JSON
     */
    public function AjaxBestBuyers($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 2:
                include "application/admin_v4_0/modules/finance/reportsOsrodek.php";
                $data = Reports4Osrodek::tabelaBestBuyers();
                break;

        }
        $this->app->ADD('response', $data);
        $this->app->SetDecorator('Ajax');
//        echo $data;
//        die();
    }

    function Invoice($idrozliczenia)
    {
        $res = $this->sdb->get_filtered_rows(
            'rozliczenia',
            '*',
            [
                ['id', $idrozliczenia, 'INT'],
                ['idpartnera', INSTALLATION, 'INT'],
                ['akceptacja', 1, 'INT'],
                ['autofaktura', 1, 'INT'],
            ]
        );

        $type = 'rozliczenie_faktura_kupon';
        if (count($res) < 1) {
            die('Błędne dane');
        }

        if (1 === (int)$_GET['kupony']) {
            if (strlen($res[0]['faktura_kupony']) > 2) {
                $faktura = $res[0]['faktura_kupony'];
            } else {
                $faktura = self::_prepareInvoiceKupon($res[0]['mgoid']);
                if (false === $faktura) {
                    die('Problem z fakturą');
                }
                $this->app->LogInsert('Faktura', $idrozliczenia,
                    'Faktura za kupony do rozliczenia ' . $idrozliczenia . ' za okres ' . $res[0]['nazwa'] . ' została wygenerowana');
                Partner::sendInvoiceEmail($res[0]['nazwa'], $res[0]['idpartnera']);
                $this->sdb->update_rows('rozliczenia', array(array('faktura_kupony', $faktura, 'STRING')), array(array('id', $idrozliczenia, 'INT')));
            }
        } elseif (1 === (int)$_GET['uwp']) {
            if (strlen($res[0]['faktura_kupony']) > 2) {
                $faktura = $res[0]['faktura_kupony'];
            } else {
                $faktura = self::_prepareInvoiceUWP($res[0]['mgoid']);
                if (false === $faktura) {
                    die('Problem z fakturą');
                }
                $this->app->LogInsert('Faktura', $idrozliczenia,
                    'Faktura za kupony do rozliczenia ' . $idrozliczenia . ' za okres ' . $res[0]['nazwa'] . ' została wygenerowana');
                Partner::sendInvoiceEmail($res[0]['nazwa'], $res[0]['idpartnera']);
                $this->sdb->update_rows('rozliczenia', array(array('faktura_kupony', $faktura, 'STRING')), array(array('id', $idrozliczenia, 'INT')));
            }
        } else {
            if (strlen($res[0]['faktura']) > 2) {
                $faktura = $res[0]['faktura'];
                $type = 'rozliczenie_faktura';
            } else {
                $faktura = self::_prepareInvoice($res[0]['mgoid']);
                if (false === $faktura) {
                    die('Problem z fakturą');
                }
                $type = 'rozliczenie_faktura';
                $this->app->LogInsert('Faktura', $idrozliczenia,
                    'Faktura do rozliczenia ' . $idrozliczenia . ' za okres ' . $res[0]['nazwa'] . ' została wygenerowana');
                Partner::sendInvoiceEmail($res[0]['nazwa'], $res[0]['idpartnera']);
                $this->sdb->update_rows('rozliczenia', array(array('faktura', $faktura, 'STRING')), array(array('id', $idrozliczenia, 'INT')));
            }
        }
        $link = App::GenerateLink($type, $idrozliczenia);
        header('Location:' . $link);
        die();
    }

    public static function _prepareInvoiceWP($mgoid, $rodzaj = 'faktura')
    {
        $mgo = Mymongo::activate();
        $rid = new MongoId($mgoid);
        $cursor = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $cursor = $mgo->Mgo2array($cursor);
        $sp = $cursor['sprzedawca_dane'];
        $spmeta = Tools::GetPartnerMetadata($cursor['sprzedawca']);
        $dane = array();
        $dane['sprzedawca'] = array(
            'nazwa' => $sp['name'],
            'miasto' => $sp['city'],
            'kod' => $sp['postalcode'],
            'adres' => $sp['address'],
            'NIP' => $sp['nip'],
            'numer_konta' => $sp['bankaccount'],
            'symbol' => $sp['skrot'], //nowość
            'logo' => '' //nowość - url do logo na fakturę, rozmiar 250x80 px
        );
        if ($spmeta['logo_file_voucher']) {
            $dane['sprzedawca']['logo'] = SITE_URL . $spmeta['logo_file_voucher'];
        } elseif ($spmeta['logo_file']) {
            $dane['sprzedawca']['logo'] = SITE_URL . $spmeta['logo_file'];
        }
        $nabywca = $cursor['partner_dane'];

        $dane['nabywca'] = array(
            'nazwa' => $nabywca['name'],
            'miasto' => $nabywca['city'],
            'kod' => $nabywca['postalcode'],
            'adres' => $nabywca['address'],
            'NIP' => $nabywca['nip']
        );
        $dane['wystawca'] = $dane['sprzedawca'];
        $dane['data_sp'] = $cursor['datado'];
        $dane['data_fak'] = date('Y-m-d');
        $dane['metoda_platnosci'] = 'transfer';
        $dane['termin_platnosci'] = '14'; //zmiana na liczbę dni
        $dane['uwagi'] = "";
        $dane['szablon'] = "partner";
        $dane['rodzajdokumentu'] = $rodzaj;
        foreach ($cursor['zestawienie'] as $k => $vo) {
            foreach ($vo['warianty'] as $v) {
                $pozycje[] = array(
                    'nazwa_produktu' => $vo['nazwa'],
                    'PKWIU' => '',
                    'cjbrutto' => $v['cjbrutto'],
                    'ilosc' => $v['ilosc'],
                    'stvat' => strtoupper($vo['stvat'])
                );
            }
        }
        $dane['pozycje'] = $pozycje;
        $plik = Faktury::faktura($dane);
        $nrfaktury = Faktury::$fnumer;
        return array('plik' => $plik, 'nrfaktury' => $nrfaktury);
    }

    public static function _prepareInvoice($mgoid, $rodzaj = 'samofakturowanie')
    {
        $mgo = Mymongo::activate();
        $rid = new MongoId($mgoid);
        $cursor = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $cursor = $mgo->Mgo2array($cursor);
        $op = [];
        $total = 0;
        if (!is_array($cursor['operacje'])) {
            $cursor['operacje'] = [];
        }
        foreach ($cursor['operacje'] as $k => $v) {
            $op[$v['vat']]['brutto'] += $v['koszty_brutto'];
            $total += $v['koszty_brutto'];
        }
        if ($cursor['zwroty_operacje']) {
            foreach ($cursor['zwroty_operacje'] as $v) {
                $op[$v['vat']]['brutto'] -= round($v['kw_zwrotu_brutto'] / 100, 2);
                $total -= round($v['kw_zwrotu_brutto'] / 100, 2);
            }
        }
        if ($cursor['wp_operacje']) {
            foreach ($cursor['wp_operacje'] as $v) {
                $op[$v['vat']]['brutto'] -= round($v['brutto'] - $v['koszty_brutto'], 2);
                $total -= round($v['brutto'] - $v['koszty_brutto'], 2);
            }
        }
        $sp = Tools::GetPartnerData($cursor['partner']);
        $spmeta = Tools::GetPartnerMetadata($cursor['partner']);
        $dane = array();
        $dane['sprzedawca'] = array(
            'nazwa' => $sp['name'],
            'miasto' => $sp['city'],
            'kod' => $sp['postalcode'],
            'adres' => $sp['address'],
            'NIP' => $sp['nip'],
            'numer_konta' => $sp['bankaccount'],
            'symbol' => $sp['skrot'], //nowość
            'logo' => '' //nowość - url do logo na fakturę, rozmiar 250x80 px
        );
        if ($spmeta['logo_file_voucher']) {
            $dane['sprzedawca']['logo'] = SITE_URL . $spmeta['logo_file_voucher'];
        } elseif ($spmeta['logo_file']) {
            $dane['sprzedawca']['logo'] = SITE_URL . $spmeta['logo_file'];
        }
        $eskipass = Tools::GetSystemSettings();

        $dane['nabywca'] = array(
            'nazwa' => $eskipass['name']['value'],
            'miasto' => $eskipass['city']['value'],
            'kod' => $eskipass['postalcode']['value'],
            'adres' => $eskipass['address']['value'],
            'NIP' => $eskipass['nip']['value']
        );
        $dane['wystawca'] = $dane['nabywca'];
        $db = Database::activate();
        $dd = $db->select_r('SELECT authcode FROM ' . TABLE_PREFIX . 'digitalsig WHERE event_id ="' . $mgoid . '"');
        $dane['wystawca']['umowaSamofakturowania'] = "Faktura wystawione w imieniu i na rachunek podatnika wymienionego jako sprzedawca na podstawie umowy " . $sp['umowafaktura'] . "\n"
            . "Rozliczenie podpisane kluczem " . $dd['authcode'];

        $dane['data_sp'] = $cursor['data_faktury'];
        $dane['data_fak'] = $cursor['data_faktury'];
        $dane['metoda_platnosci'] = 'transfer';
        $dane['termin_platnosci'] = '14'; //zmiana na liczbę dni
        $dane['uwagi'] = "";
        $dane['szablon'] = "partner";
        $dane['rodzajdokumentu'] = "samofakturowanie";
        $pozycje = [];

        foreach ($op as $k => $v) {
            if ($k == 8) {
                $pozycje[] = array(
                    'nazwa_produktu' => 'Karnety',
                    'PKWIU' => '',
                    'cjbrutto' => $v['brutto'],
                    'ilosc' => '1',
                    'stvat' => $k
                );
            }
            if ($k == 23) {
                $pozycje[] = array(
                    'nazwa_produktu' => 'Sprzedaż towarów',
                    'PKWIU' => '',
                    'cjbrutto' => $v['brutto'],
                    'ilosc' => '1',
                    'stvat' => $k
                );
            }
            if ($k == 0) {
                $pozycje[] = array(
                    'nazwa_produktu' => 'Sprzedaż towarów',
                    'PKWIU' => '',
                    'cjbrutto' => $v['brutto'],
                    'ilosc' => '1',
                    'stvat' => $k
                );
            }
            if ($k === 'zw') {
                $pozycje[] = array(
                    'nazwa_produktu' => 'Sprzedaż towarów',
                    'PKWIU' => '',
                    'cjbrutto' => $v['brutto'],
                    'ilosc' => '1',
                    'stvat' => $k
                );
            }
        }
        $dane['pozycje'] = $pozycje;

        if ($total < 0) {
            return -1;
        }

        return Faktury::faktura($dane);
    }

    public static function _prepareInvoiceKupon($mgoid, $rodzaj = 'faktura')
    {
        $mgo = Mymongo::activate();
        $rid = new MongoId($mgoid);
        $cursor = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $cursor = $mgo->Mgo2array($cursor);
        if (false === isset($cursor['kupony_operacje']) or count($cursor['kupony_operacje']) === 0) {
            return false;
        }
        foreach ($cursor['kupony_operacje'] as $v) {
            $op[$v['vat']]['brutto'] -= $v['koszty_brutto'];
        }
        $sp = Tools::GetPartnerData($cursor['partner']);
        $spmeta = Tools::GetPartnerMetadata($cursor['partner']);
        $dane = array();
        $dane['nabywca'] = array(
            'nazwa' => $sp['name'],
            'miasto' => $sp['city'],
            'kod' => $sp['postalcode'],
            'adres' => $sp['address'],
            'NIP' => $sp['nip'],
            'numer_konta' => $sp['bankaccount']
        );
        $eskipass = Tools::GetSystemSettings();

        $dane['sprzedawca'] = array(
            'nazwa' => $eskipass['name']['value'],
            'miasto' => $eskipass['city']['value'],
            'kod' => $eskipass['postalcode']['value'],
            'adres' => $eskipass['address']['value'],
            'NIP' => $eskipass['nip']['value'],
            'symbol' => $eskipass['symbolOther']['value'],
            'numer_konta' => $eskipass['bankaccount']['value']
        );
        $dane['wystawca'] = $dane['sprzedawca'];
        $db = Database::activate();
        $dd = $db->select_r('SELECT authcode FROM ' . TABLE_PREFIX . 'digitalsig WHERE event_id ="' . $mgoid . '"');
//        $dane['wystawca']['umowaSamofakturowania']="Faktura wystawione w imieniu i na rachunek podatnika wymienionego jako sprzedawca na podstawie umowy ". $sp['umowafaktura']."\n"
//                . "Rozliczenie podpisane kluczem ".$dd['authcode'];

        $dane['data_sp'] = $cursor['data_faktury'];
        $dane['data_fak'] = $cursor['data_faktury'];
        $dane['metoda_platnosci'] = 'transfer';
        $dane['termin_platnosci'] = '14'; //zmiana na liczbę dni
        $dane['uwagi'] = "Rozliczenie " . $cursor['okres'] . " podpisane kluczem " . $dd['authcode'];
        $dane['szablon'] = "partner";
        $dane['rodzajdokumentu'] = "faktura";
        foreach ($op as $k => $v) {
            if ($k == 8) {
                $pozycje[] = array(
                    'nazwa_produktu' => 'Rozliczenie kuponów ' . $cursor['okres'],
                    'PKWIU' => '',
                    'cjbrutto' => $v['brutto'],
                    'ilosc' => '1',
                    'stvat' => $k
                );
            }
            if ($k == 23) {
                $pozycje[] = array(
                    'nazwa_produktu' => 'Rozliczenie kuponów ' . $cursor['okres'],
                    'PKWIU' => '',
                    'cjbrutto' => $v['brutto'],
                    'ilosc' => '1',
                    'stvat' => $k
                );
            }
            if ($k == 0) {
                $pozycje[] = array(
                    'nazwa_produktu' => 'Rozliczenie kuponów ' . $cursor['okres'],
                    'PKWIU' => '',
                    'cjbrutto' => $v['brutto'],
                    'ilosc' => '1',
                    'stvat' => $k
                );
            }
        }
        $dane['pozycje'] = $pozycje;
//        die(var_dump($dane));
        $plik = Faktury::faktura($dane);
        return $plik;
    }

    /**
     * Wystawia fakturę za sprzedaż ubezpieczeń przez WP (Eskipass -> Partner)
     * @param MongoId $mgoid
     * @param string $rodzaj
     * @return boolean
     */
    public static function _prepareInvoiceUWP($mgoid, $rodzaj = 'faktura')
    {
        $mgo = Mymongo::activate();
        $rid = new MongoId($mgoid);
        $cursor = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $cursor = $mgo->Mgo2array($cursor);
        if (false === isset($cursor['ubezpieczeniaWP']['produkty']) or count($cursor['ubezpieczeniaWP']['produkty']) === 0) {
            return 'n/a';
        }
        $sp = Tools::GetPartnerData($cursor['partner']);
        $spmeta = Tools::GetPartnerMetadata($cursor['partner']);
        $dane = array();
        $dane['nabywca'] = array(
            'nazwa' => $sp['name'],
            'miasto' => $sp['city'],
            'kod' => $sp['postalcode'],
            'adres' => $sp['address'],
            'NIP' => $sp['nip'],
            'numer_konta' => $sp['bankaccount']
        );
        $eskipass = Tools::GetSystemSettings();

        $dane['sprzedawca'] = array(
            'nazwa' => $eskipass['name']['value'],
            'miasto' => $eskipass['city']['value'],
            'kod' => $eskipass['postalcode']['value'],
            'adres' => $eskipass['address']['value'],
            'NIP' => $eskipass['nip']['value'],
            'symbol' => $eskipass['symbolOther']['value'],
            'numer_konta' => $eskipass['bankaccount']['value']
        );
        $dane['wystawca'] = $dane['sprzedawca'];
        $db = Database::activate();
        $dd = $db->select_r('SELECT authcode FROM ' . TABLE_PREFIX . 'digitalsig WHERE event_id ="' . $mgoid . '"');
//        $dane['wystawca']['umowaSamofakturowania']="Faktura wystawione w imieniu i na rachunek podatnika wymienionego jako sprzedawca na podstawie umowy ". $sp['umowafaktura']."\n"
//                . "Rozliczenie podpisane kluczem ".$dd['authcode'];

        $dane['data_sp'] = $cursor['data_faktury'];
        $dane['data_fak'] = $cursor['data_faktury'];
        $dane['metoda_platnosci'] = 'transfer';
        $dane['termin_platnosci'] = '14'; //zmiana na liczbę dni
        $dane['uwagi'] = "Rozliczenie " . $cursor['okres'] . " podpisane kluczem " . $dd['authcode'] . '. Usługi zwolnione z podatku VAT na podstawie art. 43 ust. 1 pkt. 37 ustawy o podatku od towarów i usług. ';
        $dane['szablon'] = "partner";
        $dane['rodzajdokumentu'] = "faktura";
        foreach ($cursor['ubezpieczeniaWP']['produkty'] as $k => $v) {
            $pozycje[] = array(
                'nazwa_produktu' => $v['nazwa'],
                'PKWIU' => '',
                'cjbrutto' => $v['brutto'],
                'ilosc' => $v['ilosc'],
                'stvat' => strtoupper($v['vat'])
            );
        }
        $dane['pozycje'] = $pozycje;
        $plik = Faktury::faktura($dane);
        return $plik;
    }

    /**
     * Zwraca listę biletów/karnetów w zależności od kontekstu
     * @param string $partner Kontekst
     * @param array $vars
     * @param int $strcount
     * @param int $s
     */
    public function AjaxTicketList($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 2:
                include "application/admin_v4_0/modules/finance/reportsOsrodek.php";
                $data = Reports4Osrodek::tabelaKarnety();
                break;
            case 6:
            case 1:
                include "application/admin_v4_0/modules/finance/reportsSprzedaz.php";
                $data = Reports4Sprzedaz::tabelaKarnety();
                break;

        }
        $this->app->ADD('response', $data);
        $this->app->SetDecorator('Ajax');
//        echo $data;
//        die();
    }

    /**
     * Zwraca listę ubezpieczeń w zależności od kontekstu
     * @param string $partner Kontekst
     * @param array $vars
     * @param int $strcount
     * @param int $s
     */
    public function AjaxProductList($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 9:
                include "application/admin_v4_0/modules/finance/reportsVprodukt.php";
                $data = Reports4Vprodukt::tabelaVprodukty();
                break;
            case 6:
            case 1:
                include "application/admin_v4_0/modules/finance/reportsSprzedaz.php";
                $data = Reports4Sprzedaz::tabelaVprodukty();
                break;

        }
        $this->app->ADD('response', $data);
        $this->app->SetDecorator('Ajax');
//        echo $data;
//        die();
    }

    /**
     * Zwraca listę kard w zależności od kontekstu
     * @param string $partner Kontekst
     * @param array $vars
     * @param int $strcount
     * @param int $s
     */
    public function AjaxCardList($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 6:
            case 1:
                include "application/admin_v4_0/modules/finance/reportsSprzedaz.php";
                $data = Reports4Sprzedaz::tabelaKarty();
                break;
            case 9:

                break;

        }
        $this->app->ADD('response', $data);
        $this->app->SetDecorator('Ajax');
//        echo $data;
//        die();
    }

    /**
     * Zwraca listę ubezpieczeń w zależności od kontekstu
     * @param string $partner Kontekst
     * @param array $vars
     * @param int $strcount
     * @param int $s
     */
    public function AjaxInsuList($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 1:
            case 6:
                include "application/admin_v4_0/modules/finance/reportsSprzedaz.php";
                $data = Reports4Sprzedaz::tabelaUbezpieczenia();
                break;
            case 3:
                include "application/admin_v4_0/modules/finance/reportsUbezpieczenie.php";
                $data = Reports4Ubezpieczenie::tabelaUbezpieczenia();
                break;

        }
        $this->app->ADD('response', $data);
        $this->app->SetDecorator('Ajax');
    }

    /**
     * Zwraca tabelę zamówień w zależności od kontekstu
     * @param string $partner
     * @param array $vars
     * @param int $strcount
     * @param int $s
     * @return JSON
     */
    public function AjaxReturnsList($partner, $vars, $strcount = 10, $s = 1)
    {
        switch ($partner) {
            case 'osrodek':
                include "application/admin_v4_0/modules/finance/reportsOsrodek.php";
                $data = Reports4Osrodek::tabelaZwroty();
                break;
            case 'sprzedaz':
                include "application/admin_v4_0/modules/finance/reportsSprzedaz.php";
                $data = Reports4Sprzedaz::tabelaZwroty();
                break;
            case 'ksiegowosc':
                include "reportsKsiegowosc.php";
                $data = Reports4Ksiegowosc::tabelaZwroty();
                break;
            case 'ubezpieczenie':
                include "application/admin_v4_0/modules/finance/reportsUbezpieczenie.php";
                $data = Reports4Ubezpieczenie::tabelaZwroty();
                break;
            case 'vprodukt':
                include "application/admin_v4_0/modules/finance/reportsVprodukt.php";
                $data = Reports4Vprodukt::tabelaZwroty();
                break;

        }
        echo $data;
        die();
    }

    /**
     * Zwraca listę voucherów w zależności od kontekstu
     * @param string $partner Kontekst
     * @param array $vars
     * @param int $strcount
     * @param int $s
     */
    public function AjaxVoucherList($vars)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 2:
                include "application/admin_v4_0/modules/finance/reportsOsrodek.php";
                $data = Reports4Osrodek::tabelaVouchery();
                break;
            case 1:
            case 6:
                include "application/admin_v4_0/modules/finance/reportsSprzedaz.php";
                $data = Reports4Sprzedaz::tabelaVouchery();
                break;

        }
        $this->app->ADD('response', $data);
        $this->app->SetDecorator('Ajax');
    }

    /**
     * Zwraca listę ubezpieczeń w zależności od kontekstu
     * @param string $partner Kontekst
     * @param array $vars
     * @param int $strcount
     * @param int $s
     */
    public function AjaxCouponList($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 9:
                include "application/admin_v4_0/modules/finance/reportsVprodukt.php";
                $data = Reports4Vprodukt::tabelaKupony();
                break;
            case 3:
                include "application/admin_v4_0/modules/finance/reportsUbezpieczenie.php";
                $data = Reports4Ubezpieczenie::tabelaKupony();
                break;

            case 2:
                include "application/admin_v4_0/modules/finance/reportsOsrodek.php";
                $data = Reports4Osrodek::tabelaKupony();
                break;

        }
        echo $data;
        die();
    }

    public function AjaxRaport($typ, $vars, $target = 'xls')
    {
        include('application/admin_v4_0/modules/finance/AdminFinance.php');
        $partner_meta = Tools::GetPartnerMetadata(INSTALLATION);
        if ($partner_meta['partner_wlasne_rozliczenia'] == 1) {
            $typ = 'osrodek_wlasne_rozliczenia';
            $p_franczyzowy = $partner_meta['partner_partner_franczyzowy'];
        }
        AdminFinance::StaticAjaxRaport($typ, $vars, $target, $p_franczyzowy ?? null);
    }


    /**
     * Przygotowuje wyświetlenie rozliczenia
     * @param type $typ
     */
    public function ShowRozliczenie($typ)
    {
        $id = intval($_POST['id']);
        if ($id === 0) {
            die('false');
        }
        switch ($typ) {
            case 'os':
                $this->drawRozliczenieOsrodek($id);
                break;
            case 'oswp':
                $this->drawRozliczenieOsrodekWP($id);
                break;
            case 'sp':
                $this->drawRozliczenieSprzedaz($id);
                break;
            case 'ub':
                $this->drawRozliczenieOsrodek($id);
                break;
            case 'vp':
                $this->drawRozliczenieOsrodek($id);
                break;
        }

    }


    private function drawRozliczenieOsrodek($id)
    {
        $r = $this->sdb->select_r('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia WHERE id=' . $id . ' AND idpartnera = ' . INSTALLATION);
//        die(var_dump($r, $id));
        $mgo = Mymongo::activate();
        $rid = new MongoId($r['mgoid']);
        $cursor = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $cursor = $mgo->Mgo2array($cursor);
        if ($r['akceptacja'] == 0) {
            $cursor['sigcode'] = Auth::MakeAuthHash($cursor);
            $cursor['rcode'] = $r['mgoid'];
            $cursor['rid'] = $id;
        } else {
            $cursor['pdf'] = App::GenerateLink('rozliczenia', $id);
            $cursor['rid'] = $id;
        }
        $cursor['error'] = false;
        $cursor['status'] = $r['akceptacja'];

            $cursor['operacje'] ?? $cursor['operacje'] = [];
            $cursor['wp_operacje'] ?? $cursor['wp_operacje'] = [];
        echo json_encode($cursor);
        die();
    }

    private function drawRozliczenieSprzedaz($id)
    {
        $r = $this->sdb->select_r('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia WHERE id=' . $id . ' AND idpartnera = ' . INSTALLATION);
//        die(var_dump($r, $id));
        $mgo = Mymongo::activate();
        $rid = new MongoId($r['mgoid']);
        $cursor = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $cursor = $mgo->Mgo2array($cursor);
        if ($r['akceptacja'] == 0) {
            $cursor['sigcode'] = Auth::MakeAuthHash($cursor);
            $cursor['rcode'] = $r['mgoid'];
            $cursor['rid'] = $id;
        } else {
            $cursor['pdf'] = App::GenerateLink('rozliczenia', $id);
            $cursor['rid'] = $id;
        }
        $cursor['error'] = false;
        $cursor['status'] = $r['akceptacja'];
            $cursor['operacje'] ?? $cursor['operacje'] = [];
            $cursor['wp_operacje'] ?? $cursor['wp_operacje'] = [];

        echo json_encode($cursor);
        die();
    }

    private function drawRozliczenieOsrodekWP($id)
    {
        $r = $this->sdb->select_r('SELECT * FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE id=' . $id);
//        $r = $this->sdb->select_r('SELECT * FROM '.TABLE_PREFIX.'rozliczenia_partner_wp WHERE id='.$id.' AND idpartnera = '.INSTALLATION);
//        die(var_dump($r, $id));
        $mgo = Mymongo::activate();
        $rid = new MongoId($r['mgoid']);
        $cursor = $mgo->db->rozliczenia->findOne(array('_id' => $rid));
        $cursor = $mgo->Mgo2array($cursor);
        if ($r['akceptacja'] == 0) {
            $cursor['sigcode'] = Auth::MakeAuthHash($cursor);
            $cursor['rcode'] = $r['mgoid'];
            $cursor['rid'] = $id;
        } else {
            $cursor['pdf'] = $r['linkfaktury'];
            $cursor['nrfaktury'] = $r['nrfaktury'];
            $cursor['rid'] = $id;
        }
        $cursor['error'] = false;
        $cursor['status'] = $r['akceptacja'];
            $cursor['operacje'] ?? $cursor['operacje'] = [];
            $cursor['wp_operacje'] ?? $cursor['wp_operacje'] = [];
        echo json_encode($cursor);
        die();
    }


    public function DisplayOffersList()
    {
        $this->header_data['meta']['title'] = $this->app->Lang("Lista ofert", 'Partner') . " | ADMIN " . $this->headerparams['meta']['title'];
        $this->header_data['raw'][] = '<link href="' . THEMEDIR . 'assets/css/datepicker.css" rel="stylesheet">';
        Tools::activate($this->app->urlparam);
//        $redirect = urldecode($_GET['redirect']);
//        if ($redirect == '') {
//            $redirect = SITE_URL . 'admin.php?offers&a=list';
//            $redirect = urlencode($redirect);
//        }
//        $this->app->AddDecoratorData("redirect", $redirect);
        $osrodki = Tools::GetPartners(2);
        $osid = self::getPOList();
        foreach ($osrodki as $k => $v) {
            if (false === in_array($v['id'], $osid)) {
                continue;
            }
            $os[] = $v;
        }
        $params['osrodki'] = $os;
        $sprzedawcy = array_merge(Tools::GetPartners(1), Tools::GetPartners(6));
        $offp = self::getPSPList();
        foreach ($sprzedawcy as $k => $v) {
            if (false === in_array($v['id'], $offp)) {
                continue;
            }
            $sp[] = $v;
        }
        $params['sprzedawcy'] = $sp;
        $params['statusy'] = Tools::GetOfferStatuses();
        unset($params['statusy'][0], $params['statusy'][7]);
        $params['nazwa'] = $this->app->urlparam['offername'];
        $params['identyfikator'] = $this->app->urlparam['offerids'];
        $params['data_od'] = $this->app->urlparam['dataod'];
        $params['data_do'] = $this->app->urlparam['datado'];
        $params['osrodek'] = $this->app->urlparam['osrodek'];
        $params['sprzedawca'] = $this->app->urlparam['sprzedawca'];
        $params['status'] = $this->app->urlparam['status'];
        $params['ptype'] = $this->partner['partner']['partnertype'];
        if ($_SESSION['notify']) {
            $this->app->ADD($_SESSION['notify'], true);
            $this->app->ADD('notifytext', $_SESSION['notifytext']);
            unset($_SESSION['notify']);
            unset($_SESSION['notifytext']);
        }
        $this->app->ADD('params', $params);
        $this->app->ADD('module', MODULE);
//        $this->adminnavparams['selected'] = 'offers.list';

//        self::makeWidgets();
//        $this->app->SetTemplate("modules/" . MODULE . "/list.tpl");
        $this->Display('offers-list');
    }


    /**
     * Zwraca listę ofert w zależności od kontekstu
     * @param string $partner Kontekst
     * @param array $vars
     * @param int $strcount
     * @param int $s
     */
    public function AjaxOffersList($vars, $strcount = 10, $s = 1)
    {
        switch ($this->partner['partner']['partnertype']) {
            case 9:
                $kontekst = 'vprodukt';
                if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
                    $s = floor(intval($vars['iDisplayStart']) / intval($vars['iDisplayLength'])) + 1;
                    $strcount = intval($vars['iDisplayLength']);
                }
                if ($s == 0) {
                    $s = 1;
                }
                if ($strcount == 0) {
                    $strcount = 10;
                }

                $mgo = Mymongo::activate();
                if ($mgo->error) {
                    $this->moduleerror[] = "MGO DB Error";
                    $this->moduleerror[] = $mgo->error;
                    return false;
                }
                $where = array();
                $vars['offername'] != '' ? $where['offername'] = $vars['offername'] : '';
                $vars['offerids'] != '' ? $where['offerids'] = $vars['offerids'] : '';
                $vars['dataod'] != '' ? $where['dataod'] = $vars['dataod'] : '';
                $vars['datado'] != '' ? $where['datado'] = $vars['datado'] : '';
                $vars['sprzedawca'] != 0 ? $where['sprzedawca'] = (int)$vars['sprzedawca'] : '';
                $vars['status'] != 0 ? $where['status'] = (int)$vars['status'] : '';
                $vars['osrodek'] != 0 ? $where['osrodek'] = (int)$vars['osrodek'] : '';
                $where['produkty_wirtualne.producent'] = (string)INSTALLATION;
                $dane = $mgo->mget_offers_list(array(), $where, null, $strcount, false);

                $statusy = array(2, 3, 4, 5, 6, 7);
                $dane_return = array();
                $ile = count($dane);
                for ($i = 0; $i < $ile; $i++) {
                    if (false === in_array($dane[$i]['status.id'], $statusy)) {
                        continue;
                    }
                    if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
                        $dodawaj = false;
                        if (strpos(Additional::ToLower($dane[$i]['name'] . ' ' . $dane[$i]['typ']), Additional::ToLower($vars['sSearch'])) !== false) {
                            $dodawaj = true;
                        }
                        if ($dodawaj) {
                            $dane_return[] = $dane[$i];
                        }
                    } else {
                        $dane_return[] = $dane[$i];
                    }
                }

                $total = count($dane_return);
                $totalall = count($dane);
                $aColumns = array(
                    //'id',
                    'name',
                    'sprzedawcy',
                    'dataod',
                    'datado',
                    'status',
                    'id'
                );

                $sOrder = array(
                    "col" => "name",
                    "asc" => true
                );

                if (isset($vars['iSortCol_0'])) {
                    for ($i = 0; $i < intval($vars['iSortingCols']); $i++) {
                        if ($vars['bSortable_' . intval($vars['iSortCol_' . $i])] == "true") {
                            $sOrder = array(
                                "col" => $aColumns[intval($vars['iSortCol_' . $i])],
                                "asc" => ($vars['sSortDir_' . $i] === 'asc' ? true : false)
                            );
                        }
                    }
                }

                $dane_return = Additional::msort($dane_return, $sOrder['col'], $sOrder['asc']);
                $paginate = Additional::PaginateSimple($dane_return, $strcount, $s);
                $output = array(
                    "sEcho" => intval($vars['sEcho']),
                    "iTotalRecords" => $totalall,
                    "iTotalDisplayRecords" => $total,
                    "aaData" => array()
                );
                $rows = count($paginate['res']);
                for ($i = 0; $i < $rows; $i++) {
                    $acc = '';
                    $output['status.id'][] = $paginate['res'][$i]['status.id'];
                    if ($paginate['res'][$i]['status.id'] == 2) {
//                            $acc = '<a href="partner.php?' . MODULE . '&a=viewacceptoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-success btn-xs" title="edit"><i class="fa fa-edit"></i> '.App::_Lang('Akceptuj').'</a>&nbsp;';
                        $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'offer_accept_proccess WHERE userid=' . User::GetUserID() . ' AND offerparentid = "' . $paginate['res'][$i]['parentid'] . '"';
                        $res = $this->sdb->select($sql);
                        if (count($res) > 0) {
                            $acc = $paginate['res'][$i]['status'];
                        } else {
                            $acc = ' <span class="badge badge-success">ZAAKCEPTUJ</span>';
                        }
                    } else {
                        $acc = $paginate['res'][$i]['status'];
                    }
                    $output['aaData'][] = array(
                        $paginate['res'][$i]['nazwa'],
                        $paginate['res'][$i]['sprzedawcy'],
                        $paginate['res'][$i]['data_od'],
                        $paginate['res'][$i]['data_do'],
                        $acc,
                        '<a href="partner.php?' . MODULE . '&a=viewoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-info btn-xs" title="view"><i class="fa fa-edit"></i> ' . App::_Lang('Podgląd',
                            'Partner') . '</a>&nbsp;',
                    );
                }

                $data = json_encode($output);

                break;
            case 3:
                $kontekst = $this->ptype[$this->partner['partner']['partnertype']];
                if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
                    $s = floor(intval($vars['iDisplayStart']) / intval($vars['iDisplayLength'])) + 1;
                    $strcount = intval($vars['iDisplayLength']);
                }
                if ($s == 0) {
                    $s = 1;
                }
                if ($strcount == 0) {
                    $strcount = 10;
                }

                $mgo = Mymongo::activate();
                if ($mgo->error) {
                    $this->moduleerror[] = "MGO DB Error";
                    $this->moduleerror[] = $mgo->error;
                    return false;
                }
                $where = array();
                $vars['offername'] != '' ? $where['offername'] = $vars['offername'] : '';
                $vars['offerids'] != '' ? $where['offerids'] = $vars['offerids'] : '';
                $vars['dataod'] != '' ? $where['dataod'] = $vars['dataod'] : '';
                $vars['datado'] != '' ? $where['datado'] = $vars['datado'] : '';
                $vars['osrodek'] != 0 ? $where['osrodek'] = (int)$vars['osrodek'] : '';
                $vars['status'] != 0 ? $where['status'] = (int)$vars['status'] : '';
                $where['ubezpieczyciel'] = INSTALLATION;
                $dane = $mgo->mget_offers_list(array(), $where, null, $strcount, false);
//                die(var_dump($dane));
                $statusy = array(2, 3, 4, 5, 6, 7);
                $dane_return = array();
                $ile = count($dane);
                for ($i = 0; $i < $ile; $i++) {
                    if (false === in_array($dane[$i]['status.id'], $statusy)) {
                        continue;
                    }
                    if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
                        $dodawaj = false;
                        if (strpos(Additional::ToLower($dane[$i]['name'] . ' ' . $dane[$i]['typ']), Additional::ToLower($vars['sSearch'])) !== false) {
                            $dodawaj = true;
                        }
                        if ($dodawaj) {
                            $dane_return[] = $dane[$i];
                        }
                    } else {
                        $dane_return[] = $dane[$i];
                    }
                }

                $total = count($dane_return);
                $totalall = count($dane);
                $aColumns = array(
                    //'id',
                    'name',
                    'sprzedawcy',
                    'dataod',
                    'datado',
                    'status',
                    'id'
                );

                $sOrder = array(
                    "col" => "name",
                    "asc" => true
                );

                if (isset($vars['iSortCol_0'])) {
                    for ($i = 0; $i < intval($vars['iSortingCols']); $i++) {
                        if ($vars['bSortable_' . intval($vars['iSortCol_' . $i])] == "true") {
                            $sOrder = array(
                                "col" => $aColumns[intval($vars['iSortCol_' . $i])],
                                "asc" => ($vars['sSortDir_' . $i] === 'asc' ? true : false)
                            );
                        }
                    }
                }

                $dane_return = Additional::msort($dane_return, $sOrder['col'], $sOrder['asc']);
                $paginate = Additional::PaginateSimple($dane_return, $strcount, $s);
                $output = array(
                    "sEcho" => intval($vars['sEcho']),
                    "iTotalRecords" => $totalall,
                    "iTotalDisplayRecords" => $total,
                    "aaData" => array()
                );

                $rows = count($paginate['res']);
                for ($i = 0; $i < $rows; $i++) {
                    $acc = '';
                    $output['status.id'][] = $paginate['res'][$i]['status.id'];
                    $acc = $paginate['res'][$i]['status'];
//                    if ($paginate['res'][$i]['status.id'] == 2) {
////                            $acc = '<a href="partner.php?' . MODULE . '&a=viewacceptoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-success btn-xs" title="edit"><i class="fa fa-edit"></i> '.App::_Lang('Akceptuj').'</a>&nbsp;';
//                            $sql = 'SELECT * FROM '.TABLE_PREFIX.'offer_accept_proccess WHERE userid='.User::GetUserID().' AND offerparentid = "'.$paginate['res'][$i]['parentid'].'"';
//                            $res = $this->sdb->select($sql);
//                            if(count($res)>0){
//                                $acc = $paginate['res'][$i]['status'];
//                            }
//                            else $acc = ' <span class="badge badge-success">ZAAKCEPTUJ</span>';
//
//                    }
//                    else{
//                        $acc = $paginate['res'][$i]['status'];
//                    }
                    switch ($this->partner['partner']['partnertype']) {
                        case 2:
                            $pole1 = $paginate['res'][$i]['sprzedawcy'];
                            break;

                        case 1:
                        case 6:
                        case 3:
                            $pole1 = $paginate['res'][$i]['osrodki'];
                            break;
                    }
                    $output['aaData'][] = array(
                        $paginate['res'][$i]['nazwa'],
                        $pole1,
                        $paginate['res'][$i]['data_od'],
                        $paginate['res'][$i]['data_do'],
                        $acc,
                        ''
//                        '<a href="partner.php?' . MODULE . '&a=viewoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-info btn-xs" title="view"><i class="fa fa-edit"></i> '.App::_Lang('Podgląd','Partner').'</a>&nbsp;',
                    );
                }

//                $this->app->ADD('response', json_encode($output));
//                $this->app->SetDecorator('Ajax');
                $data = json_encode($output);
                break;

            case 1:
            case 6:
                $kontekst = $this->ptype[$this->partner['partner']['partnertype']];
                if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
                    $s = floor(intval($vars['iDisplayStart']) / intval($vars['iDisplayLength'])) + 1;
                    $strcount = intval($vars['iDisplayLength']);
                }
                if ($s == 0) {
                    $s = 1;
                }
                if ($strcount == 0) {
                    $strcount = 10;
                }

                $mgo = Mymongo::activate();
                if ($mgo->error) {
                    $this->moduleerror[] = "MGO DB Error";
                    $this->moduleerror[] = $mgo->error;
                    return false;
                }
                $where = array();
                $vars['offername'] != '' ? $where['offername'] = $vars['offername'] : '';
                $vars['offerids'] != '' ? $where['offerids'] = $vars['offerids'] : '';
                $vars['dataod'] != '' ? $where['dataod'] = $vars['dataod'] : '';
                $vars['datado'] != '' ? $where['datado'] = $vars['datado'] : '';
                $vars['osrodek'] != 0 ? $where['osrodek'] = (int)$vars['osrodek'] : '';
                $vars['status'] != 0 ? $where['status'] = (int)$vars['status'] : '';
                $where['sprzedawca'] = INSTALLATION;
                $dane = $mgo->mget_offers_list(array(), $where, null, $strcount, false);
//                die(var_dump($dane));
                $statusy = array(2, 3, 4, 5, 6, 7);
                $dane_return = array();
                $ile = count($dane);
                for ($i = 0; $i < $ile; $i++) {
                    if (false === in_array($dane[$i]['status.id'], $statusy)) {
                        continue;
                    }
                    if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
                        $dodawaj = false;
                        if (strpos(Additional::ToLower($dane[$i]['name'] . ' ' . $dane[$i]['typ']), Additional::ToLower($vars['sSearch'])) !== false) {
                            $dodawaj = true;
                        }
                        if ($dodawaj) {
                            $dane_return[] = $dane[$i];
                        }
                    } else {
                        $dane_return[] = $dane[$i];
                    }
                }

                $total = count($dane_return);
                $totalall = count($dane);
                $aColumns = array(
                    //'id',
                    'name',
                    'sprzedawcy',
                    'dataod',
                    'datado',
                    'status',
                    'id'
                );

                $sOrder = array(
                    "col" => "name",
                    "asc" => true
                );

                if (isset($vars['iSortCol_0'])) {
                    for ($i = 0; $i < intval($vars['iSortingCols']); $i++) {
                        if ($vars['bSortable_' . intval($vars['iSortCol_' . $i])] == "true") {
                            $sOrder = array(
                                "col" => $aColumns[intval($vars['iSortCol_' . $i])],
                                "asc" => ($vars['sSortDir_' . $i] === 'asc' ? true : false)
                            );
                        }
                    }
                }

                $dane_return = Additional::msort($dane_return, $sOrder['col'], $sOrder['asc']);
                $paginate = Additional::PaginateSimple($dane_return, $strcount, $s);
                $output = array(
                    "sEcho" => intval($vars['sEcho']),
                    "iTotalRecords" => $totalall,
                    "iTotalDisplayRecords" => $total,
                    "aaData" => array()
                );
                $rows = count($paginate['res']);
                for ($i = 0; $i < $rows; $i++) {
                    $acc = '';
                    $output['status.id'][] = $paginate['res'][$i]['status.id'];
                    if ($paginate['res'][$i]['status.id'] == 2) {
//                            $acc = '<a href="partner.php?' . MODULE . '&a=viewacceptoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-success btn-xs" title="edit"><i class="fa fa-edit"></i> '.App::_Lang('Akceptuj').'</a>&nbsp;';
                        $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'offer_accept_proccess WHERE userid=' . User::GetUserID() . ' AND offerparentid = "' . $paginate['res'][$i]['parentid'] . '"';
                        $res = $this->sdb->select($sql);
                        if (count($res) > 0) {
                            $acc = $paginate['res'][$i]['status'];
                        } else {
                            $acc = ' <span class="badge badge-success">ZAAKCEPTUJ</span>';
                        }
                    } else {
                        $acc = $paginate['res'][$i]['status'];
                    }
                    switch ($this->partner['partner']['partnertype']) {
                        case 2:
                            $pole1 = $paginate['res'][$i]['sprzedawcy'];
                            break;

                        case 1:
                        case 6:
                            $pole1 = $paginate['res'][$i]['osrodki'];
                            break;
                    }
                    $output['aaData'][] = array(
                        $paginate['res'][$i]['nazwa'],
                        $pole1,
                        $paginate['res'][$i]['data_od'],
                        $paginate['res'][$i]['data_do'],
                        $acc,
                        ''
//                        '<a href="partner.php?' . MODULE . '&a=viewoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-info btn-xs" title="view"><i class="fa fa-edit"></i> '.App::_Lang('Podgląd','Partner').'</a>&nbsp;',
                    );
                }

//                $this->app->ADD('response', json_encode($output));
//                $this->app->SetDecorator('Ajax');
                $data = json_encode($output);
                break;

            case 2:
                $kontekst = 'osrodek';
                if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
                    $s = floor(intval($vars['iDisplayStart']) / intval($vars['iDisplayLength'])) + 1;
                    $strcount = intval($vars['iDisplayLength']);
                }
                if ($s == 0) {
                    $s = 1;
                }
                if ($strcount == 0) {
                    $strcount = 10;
                }

                $mgo = Mymongo::activate();
                if ($mgo->error) {
                    $this->moduleerror[] = "MGO DB Error";
                    $this->moduleerror[] = $mgo->error;
                    return false;
                }
                $where = array();
                $vars['offername'] != '' ? $where['offername'] = $vars['offername'] : '';
                $vars['offerids'] != '' ? $where['offerids'] = $vars['offerids'] : '';
                $vars['dataod'] != '' ? $where['dataod'] = $vars['dataod'] : '';
                $vars['datado'] != '' ? $where['datado'] = $vars['datado'] : '';
                $vars['sprzedawca'] != 0 ? $where['sprzedawca'] = (int)$vars['sprzedawca'] : '';
                $vars['status'] != 0 ? $where['status'] = (int)$vars['status'] : '';
                $where['osrodek'] = INSTALLATION;
                $dane = $mgo->mget_offers_list(array(), $where, null, $strcount, false);

                $statusy = array(2, 3, 4, 5, 6, 7);
                $dane_return = array();
                $ile = count($dane);
                for ($i = 0; $i < $ile; $i++) {
                    if (false === in_array($dane[$i]['status.id'], $statusy)) {
                        continue;
                    }
                    if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
                        $dodawaj = false;
                        if (strpos(Additional::ToLower($dane[$i]['name'] . ' ' . $dane[$i]['typ']), Additional::ToLower($vars['sSearch'])) !== false) {
                            $dodawaj = true;
                        }
                        if ($dodawaj) {
                            $dane_return[] = $dane[$i];
                        }
                    } else {
                        $dane_return[] = $dane[$i];
                    }
                }

                $total = count($dane_return);
                $totalall = count($dane);
                $aColumns = array(
                    //'id',
                    'name',
                    'sprzedawcy',
                    'dataod',
                    'datado',
                    'status',
                    'id'
                );

                $sOrder = array(
                    "col" => "name",
                    "asc" => true
                );

                if (isset($vars['iSortCol_0'])) {
                    for ($i = 0; $i < intval($vars['iSortingCols']); $i++) {
                        if ($vars['bSortable_' . intval($vars['iSortCol_' . $i])] == "true") {
                            $sOrder = array(
                                "col" => $aColumns[intval($vars['iSortCol_' . $i])],
                                "asc" => ($vars['sSortDir_' . $i] === 'asc' ? true : false)
                            );
                        }
                    }
                }

                $dane_return = Additional::msort($dane_return, $sOrder['col'], $sOrder['asc']);
                $paginate = Additional::PaginateSimple($dane_return, $strcount, $s);
                $output = array(
                    "sEcho" => intval($vars['sEcho']),
                    "iTotalRecords" => $totalall,
                    "iTotalDisplayRecords" => $total,
                    "aaData" => array()
                );
                $rows = count($paginate['res']);
                for ($i = 0; $i < $rows; $i++) {
                    $acc = '';
                    $output['status.id'][] = $paginate['res'][$i]['status.id'];
                    if ($paginate['res'][$i]['status.id'] == 2) {
//                            $acc = '<a href="partner.php?' . MODULE . '&a=viewacceptoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-success btn-xs" title="edit"><i class="fa fa-edit"></i> '.App::_Lang('Akceptuj').'</a>&nbsp;';
                        $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'offer_accept_proccess WHERE userid=' . User::GetUserID() . ' AND offerparentid = "' . $paginate['res'][$i]['parentid'] . '"';
                        $res = $this->sdb->select($sql);
                        if (count($res) > 0) {
                            $acc = $paginate['res'][$i]['status'];
                        } else {
                            $acc = ' <span class="badge badge-success">ZAAKCEPTUJ</span>';
                        }
                    } else {
                        $acc = $paginate['res'][$i]['status'];
                    }
                    $output['aaData'][] = array(
                        $paginate['res'][$i]['nazwa'],
                        $paginate['res'][$i]['sprzedawcy'],
                        $paginate['res'][$i]['data_od'],
                        $paginate['res'][$i]['data_do'],
                        $acc,
                        '<a href="partner.php?' . MODULE . '&a=viewoffer&id=' . $paginate['res'][$i]['parentid'] . '&_rtd=1" class="btn btn-info btn-xs" title="view"><i class="fa fa-edit"></i> ' . App::_Lang('Podgląd',
                            'Partner') . '</a>&nbsp;',
                    );
                }

//                $this->app->ADD('response', json_encode($output));
//                $this->app->SetDecorator('Ajax');
                $data = json_encode($output);
                break;

        }
        echo $data;
        die();
    }

    public function CanAcceptOffer($offer)
    {
        if (intval(User::GetUserPerm()) === 100) {
            return 'Y';
        }
        $osrodki_cu = $this->_getUserPartners();
        $partners = array_merge(array_keys($offer['osrodki']), array_keys($this->ReindexArray($offer['partnerzy_sp'])));
        $can = 'N';
        foreach ($osrodki_cu as $v) {
            if (in_array($v, $partners)) {
                $can = 'Y';
                break;
            }
        }
        if ($can == 'Y') {
            $is = $this->IsAcceptProccessed($offer['_id']['$id']);
            if (false === $is) {
                return false;
            }
            if ($is === 0) {
                $can = 'N';
            }
        }
        return $can;
    }


    public function ViewOffer($id, $context)
    {
        $mgo = Mymongo::activate();
        $offer = $mgo->mget_offer_details($id);
        include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');
        include_once('application/admin_v4_0/modules/offers/AdminOffers.php');
        $karty = $offer['karty'] ?: array();
        foreach ($karty as $k => $v) {
            $l = Tools::GetPartnerProductLangs($v['typ_karty'], 'hash');
            $v['typ_karty'] = $l['nazwa'][LANG];
            $this->app->ADD('pr_kart', Tools::getPartners(8));
            $this->app->ADD('typy_kart', Tools::GetPartnerAllProducts($v['producent_kart']));
            $this->app->ADD('producent_kart', $v['producent_kart']);
            $this->app->ADD('postback', $v);
            $this->app->ADD('cardid', $k + 1);
            $this->app->ADD('context', $context);
            $this->app->ADD('pzawieszone', $pzawieszone);
            $this->app->SetTemplate('modules/partner/offers/view_karty.tpl');
            $doc = new DecoratorSmarty($this->app);
            $kartys[] = $doc->output;
            $this->app->ResetDecoratorData();
        }
        $karnety = $offer['karnety'] ?: array();
        foreach ($karnety as $k => $karnet) {
            $karnet['obowiazuje']['time_starth'] = substr($karnet['obowiazuje']['time_start'], 0, 2);
            $karnet['obowiazuje']['time_startm'] = substr($karnet['obowiazuje']['time_start'], 2, 2);
            $karnet['obowiazuje']['time_stoph'] = substr($karnet['obowiazuje']['time_stop'], 0, 2);
            $karnet['obowiazuje']['time_stopm'] = substr($karnet['obowiazuje']['time_stop'], 2, 2);
            $this->app->ADD('ticketid', $k + 1);
            $this->app->ADD('context', $context);
            $this->app->ADD('pzawieszone', $pzawieszone);
            if ($karnet['prowizja'][0]['grupa']) {
                $this->app->ADD('grupaobrotowa',
                    $this->sdb->get_field_val('prowizja_grupy', 'nazwa', array(array('id', $karnet['prowizja'][0]['grupa'], 'INT'))));

            } else {
                $this->app->ADD('grupaobrotowa', 'brak');
            }
            $sql_r = 'SELECT id, nazwa FROM ' . TABLE_PREFIX . 'prowizja_grupy WHERE id_oferty="' . $id . '"';
            $this->app->ADD('grupyobrotowe', $this->sdb->select($sql_r));
            $this->app->ADD('listakart', Tools::GetPartnerTypeProducts(2));
            $this->app->ADD('postback', $karnet);
            $this->app->SetTemplate('modules/partner/offers/view_karnety.tpl');
            $doc = new DecoratorSmarty($this->app);
            $tickets[] = $doc->output;
            $this->app->ResetDecoratorData();
        }

        $ubezpieczenia = $offer['ubezpieczenia'] ?: array();
        foreach ($ubezpieczenia as $l => $ubezp) {
            $ubezpieczyciele = Tools::GetPartners(3, true);
            $sprodukty = Tools::GetPartnerTypeProducts(3, true);
            $partnersmeta = Tools::GetPartnersMetadata(3);
            foreach ($ubezpieczyciele as $k => $v) {
                foreach ($sprodukty as $kk => $vv) {
                    if ($vv['parent_id'] == $k) {
                        $pr['ubezpieczyciel'] = $v['shortname'] . ' - ' . $vv['name'];
                        $pr['partner_id'] = $v['id'];
                        $pr['produkt_hash'] = $vv['hash'];
                        $pr['czas_trwania'] = $vv['metadata']['czas_trwania'];
                        $pr['cenam_brutto'] = $vv['metadata']['cenam_brutto'];
                        $pr['cena_brutto'] = $vv['metadata']['cena_brutto'];
                        $pr['koszt_brutto'] = $vv['metadata']['koszt_brutto'];
                        $pr['koszt_vat'] = $vv['metadata']['koszt_vat'];
                        $pr['cena_vat'] = $vv['metadata']['cena_vat'];
                        $pr['czas_trwania'] = $vv['metadata']['czas_trwania'];
                        $pr['uid'] = $vv['id'];
                        $pr['nrumowy'] = $partnersmeta[$k]['nrumowy'];
                        $prod[] = $pr;
                    }
                }
            }
            $this->app->ADD('postback', $ubezp);
            $this->app->ADD('produkty', $prod);
            $this->app->ADD('insuranceid', $l + 1);
            $this->app->ADD('context', $context);
            $this->app->ADD('pzawieszone', $pzawieszone);
            $this->app->SetTemplate('modules/partner/offers/view_ubezpieczenia.tpl');
            $doc = new DecoratorSmarty($this->app);
            $insurances[] = $doc->output;
            $this->app->ResetDecoratorData();
            $prod = array();

        }

        $vouchery = $offer['vouchery'] ?: array();
//        if (!is_array($vouchery))
//            $vouchery = array();
        foreach ($vouchery as $k => $v) {
            $this->app->ADD('listakart', Tools::GetPartnerTypeProducts(2));
            $this->app->ADD('voucherid', $k + 1);
            $this->app->ADD('context', $context);
            $this->app->ADD('pzawieszone', $pzawieszone);
            if ($v['prowizja'][0]['grupa']) {
                $this->app->ADD('grupaobrotowa', $this->sdb->get_field_val('prowizja_grupy', 'nazwa', array(array('id', $v['prowizja'][0]['grupa'], 'INT'))));
            } else {
                $this->app->ADD('grupaobrotowa', 'brak');
            }
            $this->app->ADD('postback', $v);
            $this->app->SetTemplate("modules/partner/offers/view_vouchery.tpl");
            $this->app->ResetWidgetsData();
            $doc = new DecoratorSmarty($this->app);
            $vouchers[] = $doc->output;
            $this->app->ResetDecoratorData();
        }

        $vprodukty = $offer['produkty_wirtualne'] ?: array();
//        if (!is_array($vprodukty))
//            $vprodukty = array();
        foreach ($vprodukty as $k => $v) {
//            $this->EditVirtualp($k, $offerid);
            $this->app->ADD('postback', $v);
            $this->app->ADD('cardid', $k + 1);
            $this->app->ADD('context', $context);
            $this->app->ADD('pzawieszone', $pzawieszone);
            $this->app->SetTemplate("modules/partner/offers/view_virtualp.tpl");
            $this->app->ResetWidgetsData();
            $doc = new DecoratorSmarty($this->app);
            $vproducts[] = $doc->output;
            $this->app->ResetDecoratorData();
        }

        $this->app->ADD('cards', $kartys);
        $this->app->ADD('tickets', $tickets);
        $this->app->ADD('insurances', $insurances);
        $this->app->ADD('vouchers', $vouchers);
        $this->app->ADD('vprodukty', $vproducts);
        $this->app->ADD('viewmode', 'view');
        $this->app->ADD('context', $context);
        if ($offer['status']['id'] == '2') {
            $offer['sigcode'] = Auth::MakeAuthHash($offer);
        }

        if (isset($offer['przewoznicy'])) {
            $offer['przewoznicy'] = $this->ReindexArray($offer['przewoznicy'], 'hash');
        }
        if (isset($offer['partnerzy_sp'])) {
            $offer['partnerzy_sp'] = $this->ReindexArray($offer['partnerzy_sp']);
        }
        if (isset($offer['systemy_platnosci'])) {
            $offer['systemy_platnosci'] = $this->ReindexArray($offer['systemy_platnosci']);
        }
//        die(var_dump($oferta));
        $offer['offerid'] = $offer['_id']['$id'];
        $this->app->ADD('osrodki', $this->_prepareSkiresorts());
        $this->app->ADD('kurierzy', $this->_prepareDeliverers());
        $this->app->ADD('systempl', $this->_preparePaymantSystems());
        $this->app->ADD('brokers', $this->_prepareBrokers());
        $this->app->ADD('brokersfr', $this->_prepareFrBrokers());
        $this->app->ADD("postback", $offer);
//        $this->app->ADD('todo', 'update');
        $this->app->ADD('section', 'main');
        $this->app->ADD('viewmode', 'view');
        $but = AdminOffers::Buttons($offer['status']['id'], 'view');
//        $this->makeWidgets();
//        $this->app->SetTemplate("modules/" . MODULE . "/edit.tpl");

        if ($offer['status']['id'] == 2) { // jeżeli jest w akceptacji
            $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'offer_accept_proccess WHERE userid=' . User::GetUserID() . ' AND offerparentid = "' . $id . '"';
            $res = $this->sdb->select($sql);
            if (count($res) > 0) {
                unset($but['akceptuj']);
                unset($but['odrzuc']);
            }
        }

        if ($offer['forkto']) {
//            unset($but['forkuj_oferte']);
        }
        $this->app->ADD('buttons', $but);
        $this->Display('offers');
//        $this->app->SetTemplate('modules/partner/offers.tpl');
    }

    public function AcceptOffer($step)
    {
        require_once 'application/admin_v4_0/modules/offers/AdminOffers.php';
        $ad = new AdminOffers($this->app);
        $ad->AcceptOffer($step);
        header('Location:' . SITE_URL . 'partner.php?partner&a=viewoffer&id=' . $_GET['id']);
    }

    protected function AjaxAuthCheck()
    {
        $pin = $_POST['pin'];
        $authcode = $_POST['sigcode'];
        $pid = $_POST['parentid'];
        $oid = $_POST['offerid'];
        $mgo = Mymongo::activate();

        $offer = $mgo->mget_offer_details($pid);
        if ($offer['_id']['$id'] != $oid) {
            $response['error'] = 'Bad offer id';
            echo json_encode($response);
            die;
        }
        $response = Auth::Authorize($pin, $offer);
        if ($response['error']) {
            echo json_encode($response);
            die;
        }
        if ($response['code'] === $authcode) {
            $response['code'] = 'OK';
        } else {
            $response['error'] = 'CODERROR';
        }
        echo json_encode($response);
        die();
    }

    private function ReindexArray(array $array, $fname = 'id')
    {
        foreach ($array as $v) {
            $rarray[$v[$fname]] = $v;
        }
        return $rarray;
    }

    private function _preparePaymantSystems()
    {
        $this->_preparePartners();
        $systemypl = $this->partners['systemypl'];
        foreach ($systemypl as $k => $v) {
            $meta = Tools::GetPartnerMetadata($k);
            foreach ($meta as $kk => $vv) {
                $systemypl[$k][$kk] = $vv;
            }
        }
        return $systemypl;
    }

    private function _prepareSkiresorts()
    {
        $this->_preparePartners();
        $os = $this->partners['osrodkin'];
        return $os;
    }

    private function _prepareDeliverers()
    {
        $this->_preparePartners();
        $kurierzy = $this->partners['kurierzy'];
        $products_deliv = Tools::GetPartnerTypeProducts(5, true);
        foreach ($kurierzy as $k => $v) {
            foreach ($products_deliv as $kk => $vv) {
                if ($vv['parent_id'] == $k) {
                    $out['nazwa'] = $v['shortname'] . ' - ' . $vv['name'];
                    $out['netto'] = $vv['metadata']['cena_z_prowizja_netto'];
                    $out['vat'] = $vv['metadata']['vat'];
                    $out['brutto'] = $vv['metadata']['cena_z_prowizja_brutto'];
                    $out['ids'] = $k . 'p' . $vv['id'];
                    $out['kid'] = $k;
                    $out['kname'] = $v['shortname'];
                    $out['prid'] = $vv['hash'];
                    $out['prname'] = $vv['name'];
                    $prod[] = $out;
                }
            }
        }
        return $prod;
    }

    private function _prepareBrokers()
    {
        $this->_preparePartners();
        $brokers = $this->partners['brokers'];
        $brokersids = array_keys($brokers);
        $sql = 'SELECT id_parent, value, properties FROM ' . TABLE_PREFIX . 'partners_meta WHERE current=1 AND id_parent IN (' . join(',', $brokersids) . ')';
        $prow = $this->sdb->select($sql);
        foreach ($prow as $v) {
            $brokers[$v['id_parent']][$v['properties']] = number_format((float)$v['value'], 2);
        }
        return $brokers;
    }

    /**
     * Przygotowanie tablicy z danymi sprzedawców franczyzowych do wyświetlenia na stronie tworzenia oferty
     * @return type
     */
    private function _prepareFrBrokers()
    {
        $this->_preparePartners();
        $brokers = $this->partners['brokersfr'];
        $brokersids = array_keys($brokers);
        $sql = 'SELECT id_parent, value, properties FROM ' . TABLE_PREFIX . 'partners_meta WHERE current=1 AND id_parent IN (' . join(',', $brokersids) . ')';
        $prow = $this->sdb->select($sql);
        foreach ($prow as $v) {
            $brokers[$v['id_parent']][$v['properties']] = number_format((float)$v['value'], 2);
        }
        return $brokers;
    }

    private function _preparePartners()
    {
        if (!is_null($this->partners)) {
            return;
        }
        $partnerzy = Tools::GetPartners();
        if (!is_array($partnerzy)) {
            $partnerzy = array();
        }
        foreach ($partnerzy as $k => $partner) {
            switch ($partner['partnertype']) {
                case 1:
                    $brokers[$partner['id']] = $partnerzy[$k];
                    break;
                case 2:
                    $osrodkin[$partner['id']] = $partnerzy[$k];
                    break;
                case 3:
                    $ubezpieczyciele[$partner['id']] = $partnerzy[$k];
                    break;
                case 4:
                    $systemypl[$partner['id']] = $partnerzy[$k];
                    break;
                case 5:
                    $kurierzy[$partner['id']] = $partnerzy[$k];
                    break;
                case 6:
                    $brokersfr[$partner['id']] = $partnerzy[$k];
                    break;
                default:
                    break;
            }
            $partners['brokers'] = $brokers;
            $partners['brokersfr'] = $brokersfr;
            $partners['osrodkin'] = $osrodkin;
            $partners['ubezpieczyciele'] = $ubezpieczyciele;
            $partners['systemypl'] = $systemypl;
            $partners['kurierzy'] = $kurierzy;
            $this->partners = $partners;
        }
    }

    /**
     * Nowe zamówienie
     */
    public function Order()
    {
        $offer = new OfferData();

        if ($this->post['data_od']) {
            $val[] = array($this->post['data_od'], 'DATE', 'Data rozpoczęcia');
        }

        if ($this->post['data_do']) {
            $val[] = array($this->post['data_do'], 'DATE', 'Data zakończenia');
        }

        if ($val) {
            $val = Validation::ValForm($val);

            if ($val['errors']) {
                $this->alerts['warning'] = $val['warnings'];
                $data_od = 0;
                $data_do = 0;
            } else {
                if ($this->post['data_od']) {
                    $data_od = $this->post['data_od'];
                    $offer->setProductDateFrom($data_od);
                }

                if ($this->post['data_do']) {
                    $data_do = $this->post['data_do'];
                    $offer->setProductDateTo($data_do);
                }
            }
        }
        $offer->setMTime();
        $offer->setOperators('partner_offer');
        $offer->getOffer();
        $offer->addProductDates();
        $offer->addProductPrice();
        $offer->setProductAvailable(true, false);
        $offer->setProductResortsString();
        $offer->addOfferId();
        $offer->offerHasTickets();
        $offer->moveProductsUp();
        $offer->format();
        $cards_serials = array();
        $sql = 'SELECT serial FROM ' . TABLE_PREFIX . 'karty ';
        $sql .= 'WHERE id_usera = ' . USER::GetUserID();
        $this->sdb->query($sql);

        while ($row = $this->sdb->fetch_row()) {
            $cards_serials[] = $row['serial'];
        }

        $this->tpl_data['cards_serials'] = $cards_serials;
        $this->tpl_data['data_rozpoczecia'] = $data_od;
        $this->tpl_data['data_zakonczenia'] = $data_do;
        $this->tpl_data['offers_data'] = $offer->output;
//        Tools::PA($offer->output);

        $voffer = new OfferData();
        $voffer->setMTime();
        $voffer->setOperators('partner_offer_wvoucher');
        $voffer->getOffer();
//        Tools::PA($voffer->output);
        $voffer->addProductDates();
        $voffer->addProductPrice();
        $voffer->setProductAvailable(true, false);
        $voffer->setProductResortsString();
        $voffer->addOfferId();
        $voffer->offerHasTickets();
        $voffer->moveProductsUp();
        $voffer->format();

        $this->tpl_data['voffers_data'] = $voffer->output;
//        Tools::PA($voffer->output);

        $goffer = new OfferData();
        $goffer->setMTime();
        $goffer->setOperators('partner_offer_gvoucher');
        $goffer->getOffer();
//        Tools::PA($goffer->output);
        $goffer->addProductDates();
        $goffer->addProductPrice();
        $goffer->setProductAvailable(true, false);
        $goffer->setProductResortsString();
        $goffer->addOfferId();
        $goffer->offerHasTickets();
        $goffer->moveProductsUp();
        $goffer->format();
        $goffer->prepareProductGroups();
        $this->tpl_data['vgrupowe'] = $goffer->output;


        $uoffer = new OfferData();
        $uoffer->setMTime();
        $uoffer->setOperators('partner_offer_ubezpieczenia');
        $uoffer->getOffer();
        $uoffer->addProductDates();
        $uoffer->addProductPrice();
        $uoffer->setProductAvailable(true, true);
        $uoffer->setProductResortsString();
        $uoffer->addOfferId();
        $uoffer->offerHasTickets();
        $uoffer->moveProductsUp();
        $uoffer->format();
//Tools::PA($uoffer->output);
        $this->tpl_data['uoffers_data'] = $uoffer->output;
        Tools::PA($goffer->output, false, false);
        Tools::PA($voffer->output, false, false);
        $this->tpl_data['grupy_produktowe'] = $this->_getProductsGroups();
        $this->tpl_data['wallet'] = Wallet::GetWallet(INSTALLATION);
        $wallet = Wallet::StaticCheckBalance(INSTALLATION, true);
        $this->tpl_data['wallet_balance'] = Tools::AddCC($wallet) ?? 0;
//        Tools::PA($uoffer->output);
        $this->Display('order');
    }

    public function SumOrder()
    {
        $accept = true;
        $basket = new BasketData;
        $basket->GetBasket();
        $basket->getOrderCount();

        if ($basket->order_count < 1) {
            $this->Order();
        } else {
            if (!$basket->checkBasket()) {
                $this->alerts['warning'] = $basket->errors;
            }
            if (!$basket->CheckWallet()) {
                $warning = 'Brak wystarczających środków w portfelu. ';
                $warning .= 'Stan portfela: <strong>' . $basket->wallet_balance . '</strong>. ';
                $warning .= 'Wartość zamówienia: <strong>' . $basket->basket['wartosc_zamowienia_waluta_brutto_disp'] . '</strong>';
                $this->alerts['warning'][] = $warning;
                $accept = false;
            }

            $basket->SaveOrder();
            $basket->getPaymentBasketData();
            $this->tpl_data['basket'] = $basket->output;
//            die(var_export($basket->output));
            $this->tpl_data['accept'] = $accept;
//            Tools::PA($this->tpl_data);
            $this->Display('sumorder');
        }
    }

    public function AcceptOrder()
    {
        $basket = new BasketData();
        $basket->GetBasket();
        $basket->getPayment();

        if (!$basket->basket['bid']) {
            header('Location: ' . SITE_URL . 'partner.php?partner&a=sumorder');
            exit;
        }

        $basket->CheckBasket();

        if (!$basket->result && !empty($basket->errors)) {
            $this->alerts['warning'] = $basket->errors;
        }

        $basket->SetBasket();
        $basket->GetOrderCount();

        if ($basket->order_count > 0) {
            $payment_data['system_platnosci']['id'] = PAYWALLET_ID;
            $basket->setPaymentData($payment_data);
            $basket->addPaymentPaymentData();
            $order = new OrderData();
            $order->SetOrder($basket->basket);
            $order->getPayment();

            if (!$order->SetOrderForPayment()) {
                $this->alerts['warning'] = 'Podczas przygotowania zamówienia wystąpił błąd!';
            } else {
                try {
//                    dd($order->output );
                    $end_order = new Zamowienie($order->output);
                    $end_order->prepOrder();
                    $post['id_zamowienia'] = $end_order->id_zamowienia;
                    $post['id_zamowienia_mgo'] = $end_order->id_zamowienia_mgo;
                    $post['system_platnosci'] = PAYWALLET_ID; // portfel
                    $post['adres_faktury'] = $order->output['adres_faktury'];
                    $post['koncowa_wartosc_zamowienia_brutto'] = $order->output['wartosc_zamowienia']['koncowa_wartosc_zamowienia_brutto'];
                    $payment = new SetPayment(PAYWALLET_ID, $post);
                    $payment->AddSystem();
                    $payment->system->MakePayment();
                    $response = $payment->system->FormData();
                    $end_order->processOrder();
                    $payment = new SetPayment();
                    $payment->getPaymentData($response['input']['userdata']);
                    $payment->addSystem();
                    $payment->checkResponse($response['input']);
                    $payment->system->currency_id = $order->output['waluta'];
                    $payment->finishPaymentUser();
                    $this->mkRefound($order->output['zamowienia'], $payment);
                    $this->tpl_data['basket_data'] = $basket->basket;
                    $this->tpl_data['payment_success'] = true;
//                    $pdflinks = $this->GetVouchersPDF($end_order->id_zamowienia);

                    $pdflinks = Karnet::getPDFvk($end_order->id_zamowienia);
                    $pdfy = [];
                    foreach ($pdflinks as &$link) {
                        if (isset($link['pdf'])) {
                            $link['href'] = App::GenerateLink('karty_voucher', $link['id']);
                            $pdfy[] = $link;
                        }
                    }


                    // zip for more than 3 pdfs
                      if (count($pdfy) > 3) {
                        // Get the directory path where PDFs are stored
                        $pdfDirectory = dirname($pdfy[0]['pdf']);
                        $zipFileName = 'order_' . $end_order->id_zamowienia . '_pdfs.zip';
                        $zipPath = $pdfDirectory . '/' . $zipFileName;
                        
                        // Create new ZIP archive
                        $zip = new ZipArchive();
                        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
                            // Add PDFs to ZIP
                            foreach ($pdfy as $pdf) {
                                if (file_exists($pdf['pdf'])) {
                                    $fileName = basename($pdf['pdf']);
                                    $zip->addFile($pdf['pdf'], $fileName);
                                }
                            }
                            $zip->close();
                            
                            // Create proper download URL
                            // Assuming PDFs are in a web-accessible directory
                            $downloadPath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $zipPath);
                            $downloadUrl = SITE_URL . ltrim($downloadPath, '/');
                            
                            // Add ZIP info to template data
                            $this->tpl_data['zip_download'] = [
                                'href' => $downloadUrl,
                                'filename' => $zipFileName
                            ];
                          
                        }
                        // dd($this->tpl_data['zip_download']);
                    }


//                    foreach($pdflinks ?? [] as &$r){
//                        $basket = intval($r['id_produktu_zamowienie']);
//                        foreach($order->output['zamowienia'] as $z){
//                            if($z['id_basket'] == $basket){
//                                if($z['produkt_grupowy']){
//                                    $r['nazwapdf'] = $z['produkt_grupowy']['grupa_produktowa']['nazwa'];
//                                }
//                                else{
//                                    foreach($z['voucher'] as $v){
//                                        if($v['id_product'] == $r['id_produktu_zamowienie']){
//                                            $r['nazwapdf'] = $v['nazwa'];
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                        if(!$r['nazwapdf']) $r['nazwapdf'] = 'Bilet';
//                    }
                    $this->tpl_data['vouchers_links'] = $pdfy;
                    $this->SendWPOrderMails($order->output, $end_order->id_zamowienia, $this->tpl_data['vouchers_links']);

//                    Tools::PA($order->output);
                } catch (Exception $e) {
                    $this->alerts['warning'] = $e->getMessage();
                }
            }
        } else {
            $this->alerts['warning'] = 'Brak zamówień w koszyku!';
            $this->tpl_data['payment_succes'] = false;
            $this->tpl_data['basket_data'] = false;
        }
        $this->Display('acceptorder', 'Akceptacja zamówienia');
    }

    public function SendWPOrderMails($zamowienia, $id_zamowienia, $vouchers = array())
    {
        $mails = array();
//        dd($zamowienia,$vouchers);
        $files = [];
        foreach ($zamowienia['zamowienia'] as $zam) {
            if ($zam['p_type'] == 'ubezpieczenie') {
                foreach ($zam['ubezpieczenie'] as $u) {
                    if ($u['email'] and $u['email'] != '') {
                        $mails[$u['email']]['ubezpieczenia'][] = '<tr><td>' . $u['nazwa'] . '</td><td>' . $u['numer_polisy'] . '</td><td>' . $u['imie'] . ' ' . $u['nazwisko'] . '</td><td>' . $u['data_rozpoczecia'] . '</td></tr>';
                    }
                }
            }
            if ($zam['p_type'] == 'voucher') {
                foreach ($zam['voucher'] as $u) {
                    if ($u['useremail'] and $u['useremail'] != '') {
                        foreach ($vouchers as $v) {
                            if ($v['id_produktu_zamowienie'] == $u['id_product']) {
                                $link = Voucher::getPdfLink($v['id'], 'partner');
                            }
                        }
                        $mails[$u['useremail']]['vouchery'][] = '<tr><td>' . $u['nazwa'] . '</td><td>' . $v['serial'] . '</td><td><a href="' . $link . '">Link do vouchera</a></td></tr>';
                    }

                }
            }
            if ($zam['p_type'] == 'karnet') {
                foreach ($zam['karnet'] as $u) {
                    if ($u['source_karty'] !== 'voucher' || empty($u['useremail'])) {
                        continue;
                    }
                    foreach ($vouchers as $v) {
                        if ($v['id_produktu_zamowienie'] == $u['id_product']) {
                            $link = $v['href'];
                            $files[] = ['nazwa' => 'Karnet_' . $v['serial'] . '.pdf', 'plik' => $v['pdf']];
                            $mails[$u['useremail']]['karnety'][] = '<tr><td>' . $u['nazwa'] . '</td><td>' . $v['serial'] . '</td><td><a href="' . $link . '">Link do karnetu</a></td></tr>';
                        }
                    }

                }
            }
        }
        if (DEBUG) {
            file_put_contents('mailsx.txt', var_export($mails, true));
        }
        if (count($mails) > 0) {
            $system = Tools::GetSystemSettings();
            Mailsend::activate();
            foreach ($mails as $k => $m) {
                $ub = false;
                $vo = false;
                $ka = '';
                $params = array();
                if ($m['ubezpieczenia']) {
                    $ub = '<strong>Ubezpieczenia:</strong><br><table style="width: 100%; font-family: sans-serif; font-size: 13px; border: 1px solid lightgrey; border-collapse: collapse;" caption="Ubezpieczenia"><tr><th>Nazwa</th><th>Polisa</th><th>Ubezpieczony</th><th>Rozpoczęcie</th></tr>' . join(PHP_EOL,
                            $m['ubezpieczenia']) . '</table>';

                }
                if ($m['vouchery']) {
                    $vo = '<strong>Vouchery:</strong><br><table style="width: 100%; font-family: sans-serif; font-size: 13px; border: 1px solid lightgrey; border-collapse: collapse;" caption="Vouchery"><tr><th>Nazwa</th><th>Nr vouchera</th><th>Link</th></tr>' . join(PHP_EOL,
                            $m['vouchery']) . '</table>';
                }
                if ($m['karnety']) {
                    $ka = '<strong>Karnety:</strong><br><table style="width: 100%; font-family: sans-serif; font-size: 13px; border: 1px solid lightgrey; border-collapse: collapse;" caption="Karnety"><tr><th>Nazwa</th><th>Nr karty</th><th>Link</th></tr>' . join(PHP_EOL,
                            $m['karnety']) . '</table>';
                }


                $params['email'] = $k;
                if ($ub) {
                    $params['ubezpieczenie'] = true;
                }
                $params['lang'] = LANG;
                $params[md5('%SITE_URL%')] = $system['siteUrl']['value'];
                $params[md5('%SITE_NAME%')] = $system['siteName']['value'];
                $params[md5('%SITE_EMAIL%')] = $system['bokEmail']['value'];
                $params[md5('%ORDER_ID%')] = $id_zamowienia;
                $params[md5('%ORDER_DATE%')] = $zamowienia['data_zamowienia'];
                $params[md5('%ORDER_DETAILS%')] = $ub . '<br>' . $vo . '<br>' . $ka;
                $params['files'] = $files;

                //  Wysyłanie powiadomienia o nowym zamówieniu
                if (false === Mailsend::SendEmail('36_voucherlink', $params)) {
                    $this->app->LogInsert('Mailsend Error', $id_zamowienia, 'Błęd wysyłania meila. Zam: ' . $id_zamowienia . ', adres: ' . $k);
                    if (DEBUG) {
                        file_put_contents('mserr.txt', date('Y-m-d H:i:s') . "\tBłąd wysyłania 36_bvoucher\n", FILE_APPEND);
                    }
                }
            }
        }

    }

    private function mkRefound($zamowienie, &$payment)
    {
        foreach ($zamowienie as $zam) {
            if ($zam['p_type'] == 'ubezpieczenie') {
                foreach ($zam['ubezpieczenie'] as $u) {
                    $payment->system->refoundBalance('ubezpieczenie ' . strip_tags($u['nazwa']), intval(100 * $u['cena']['cena_brutto']));
                }
            }
        }
    }

    public function GetVouchersPDF($orderid)
    {
        $sql = 'SELECT * FROM ' . TABLE_PREFIX . 'vouchery WHERE id_zamowienia = ' . $orderid . ' GROUP BY pdf';
        $res = $this->sdb->select($sql);
        return $res;
    }

    /**
     * Historia zamówień
     */
    public function History()
    {
        $postback = $this->app->postparam;

        if ($postback['data_od'] != '') {
            $val[] = array($postback['data_od'], 'DATE', 'Data od');
            $m_data_od = $postback['data_od'] . ' 00:00:01';
        } else {
//            $date_minus = time() - ( 59 + ( 60 * 59 ) + ( 60 * 23 * 60 ) );
//            $m_data_od = date( 'Y-m-d H:i:s', $date_minus );
            $m_data_od = date('Y-m-d H:i:s', strtotime('yesterday 00:00:01'));
            $postback['data_od'] = date('Y-m-d', strtotime('yesterday'));
        }

        if ($postback['data_do']) {
            $val[] = array($postback['data_do'], 'DATE', 'Data zakończenia');
            $date_plus = strtotime($postback['data_do'] . ' 23:59:59');
            $m_data_do = date('Y-m-d H:i:s', $date_plus);
        }

        if ($val) {
            $val = Validation::ValForm($val);

            if ($val['errors']) {
                $this->alerts['warning'] = $val['warnings'];
                $postback['data_od'] = 0;
                $postback['data_do'] = 0;
            }
        }

        $up = new UserProductsData();

        $operators[]['$match']['id_user'] = USER::GetUserID();

        if ($m_data_od) {
            $operators[]['$match']['data_zamowienia']['$gte'] = $m_data_od;
        }

        if ($postback['data_do']) {
            $operators[]['$match']['data_zamowienia']['$lte'] = $m_data_do;
        }

        $operators[]['$project'] = array(
            'data_zamowienia' => 1,
            'kw' => '$wartosc_zamowienia.koncowa_wartosc_zamowienia_brutto'
        );

        $operators[]['$sort'] = array(
            'data_zamowienia' => -1
        );

        $up->SetOperators($operators);
        $up->GetOrder();
        $up->AddStringId();
        $up->AddSdbId();
        $up->GetStatus();
        $up->Format();
        if (!$up->result) {
            $this->alerts['warning'][] = 'Podczas pobierania danych wystąpił błąd';
        }

        $this->tpl_data['postback'] = $postback;

        $this->tpl_data['orders_data'] = $up->output;


    

        $this->Display('history', 'Historia zamówień');
    }

    /**
     * Panel kasjera
     */
    public function Kasa()
    {
        $postback = $this->app->postparam;
        $this->Display('kasa', 'Panel kasjera');
    }

    /**
     * Lista faktur
     */
    public function Faktury($id = false)
    {
        if ($this->partner['partner']['partnertype'] == 2) {
//            $id = (int)$id;
            $id = $this->partner['partner']['id'];
            $partner = Tools::GetPartnerData(intval($id));
//            $sql = 'SELECT DISTINCT rok FROM '.TABLE_PREFIX.'rozliczenia_partner_wp WHERE idpartnera = '.$id;
        } else {
            $id = $this->partner['partner']['id'];
            $partner = $this->partner['partner'];
//            $sql = 'SELECT DISTINCT rok FROM '.TABLE_PREFIX.'rozliczenia_partner_wp WHERE idpartnera = '.$id;
        }
        $fvall = $this->sdb->select_r('SELECT COUNT(id) AS ile, SUM(netto) as netto, SUM(brutto) as brutto FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE `idpartnera` = ' . intval($id));
        $fvall['netto'] = number_format($fvall['netto'] / 100, 2);
        $fvall['brutto'] = number_format($fvall['brutto'] / 100, 2);
        $fvall['vat_wart'] = number_format($fvall['vat_wart'] / 100, 2);

//	$partner = Tools::GetPartnerData(intval($id));

        $cond = array();
        if ($_GET['dataod'] != "") {
            $cond[] = "ts >= '" . date("Y-m-d", intval(strtotime($_GET['dataod']))) . " 00:00:00'";
        }
        if ($_GET['datado'] != "") {
            $cond[] = "ts <= '" . date("Y-m-d", intval(strtotime($_GET['datado']))) . " 23:59:59'";
        }
        $cond[] = "1=1";
        $fv = $this->sdb->select("SELECT * FROM " . TABLE_PREFIX . "rozliczenia_partner_wp WHERE `idpartnera` = " . intval($id) . " AND " . implode(" AND ",
                $cond) . " ORDER BY id DESC");

        for ($i = 0, $iMax = count($fv); $i < $iMax; $i++) {
            $fv[$i]['dataodiso'] = date("Y-m-d", $fv[$i]['dataod']);
            $fv[$i]['datadoiso'] = date("Y-m-d", $fv[$i]['datado']);
            $fv[$i]['dataoplacenia'] = ($fv[$i]['dataoplacenia'] > 0 ? date("Y-m-d", $fv[$i]['dataoplacenia']) : "-");

            $fv[$i]['lp'] = $i + 1;

            $fv[$i]['netto'] = number_format($fv[$i]['netto'] / 100, 2);
            $fv[$i]['brutto'] = number_format($fv[$i]['brutto'] / 100, 2);
            $fv[$i]['vat_wart'] = number_format($fv[$i]['vat_wart'] / 100, 2);

            $fv[$i]['hash'] = md5('d5a7r8ar90te3rwaR' . $fv[$i]['id'] . USER::GetUserID());
        }
        $this->app->ADD('fv', $fv);
        $this->app->ADD('fvall', $fvall);
        $this->app->ADD('partner', $partner);
        $this->app->ADD('opartner', $this->partner);
        $this->app->ADD('params', $_GET);

        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_faktury.tpl");
        $this->Display('faktury', 'Lista faktur');
    }

    public function FakturyOsrodka()
    {
        if ($this->partner['partner']['partnertype'] == 2 || $this->partner['partner']['partnertype'] == '2') {
            $franczyzowi = $this->sdb->select_r("SELECT value FROM " . TABLE_PREFIX . "partners_meta WHERE id_parent =" . $this->partner['partner']['id'] . " AND properties = 'partner_partner_franczyzowy'");
            $partnerzyFranczyzowi = $this->sdb->select("SELECT id, shortname FROM " . TABLE_PREFIX . "partners WHERE `id` IN (" . $franczyzowi['value'] . ")");
            $this->app->ADD('partnerzywp', $partnerzyFranczyzowi ?? []);
        }

        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/fakturyLista.tpl");
        $this->Display('faktury', 'Lista faktur');
    }

    public function FakturyOsrodkaDt()
    {
        $partnerId = $this->partner['partner']['id'];
        $vars = $_GET;
        if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
            $s = floor((int)$vars['iDisplayStart'] / (int)$vars['iDisplayLength']) + 1;
            $strcount = (int)$vars['iDisplayLength'];
        }
        if ($s === 0) {
            $s = 1;
        }
        if ($strcount === 0) {
            $strcount = 10;
        }

        $columny = [
            'fv.sprzedawca',
            'fv.numer_fv',
            'fv.nabywca',
            'us.email',
            'fv.data_wystawienia_fv',
            'fv.kwota_fv',
            'fv.id_zamowienia',
        ];

        $partnerFranczyzowiSQL = "SELECT value FROM " . TABLE_PREFIX . "partners_meta WHERE id_parent = " . $partnerId . " AND properties= 'partner_partner_franczyzowy' AND current=1";
        $partnerFranczyzowy = $this->sdb->select_r($partnerFranczyzowiSQL)['value'];

        if (!empty($partnerFranczyzowy)) {
            $sql = "SELECT fv.*, us.email, zam.id as id_zamowienia FROM " . TABLE_PREFIX . "faktury fv"
                . ' LEFT JOIN ' . TABLE_PREFIX . 'zamowienia zam ON zam.id=fv.id_zamowienia'
                . ' LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik us ON us.id=zam.id_usera'
                . ' WHERE fv.installation IN (' . $partnerFranczyzowy . ',' . $partnerId . ')';
            $sql_calc = "SELECT count(*) as ile FROM " . TABLE_PREFIX . "faktury fv"
                . ' LEFT JOIN ' . TABLE_PREFIX . 'zamowienia zam ON zam.id=fv.id_zamowienia'
                . ' LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik us ON us.id=zam.id_usera'
                . ' WHERE fv.installation IN (' . $partnerFranczyzowy . ',' . $partnerId . ')';
        } else {
            $sql = "SELECT fv.*, us.email, zam.id as id_zamowienia FROM " . TABLE_PREFIX . "faktury"
                . ' LEFT JOIN ' . TABLE_PREFIX . 'zamowienia zam ON zam.id=fv.id_zamowienia '
                . ' LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik us ON us.id=zam.id_usera'
                . ' WHERE fv.installation = ' . $partnerId;
            $sql_calc = "SELECT count(*) as ile FROM " . TABLE_PREFIX . "faktury"
                . ' LEFT JOIN ' . TABLE_PREFIX . 'zamowienia zam ON zam.id=fv.id_zamowienia '
                . ' LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik us ON us.id=zam.id_usera'
                . ' WHERE fv.installation = ' . $partnerId;
        }
        $dataod = false;
        $datado = false;
        $partner = false;
        if ($vars['dataod'] != '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $vars['dataod'])) {
                $dataod = $vars['dataod'];
                $sql .= ' AND fv.data_wystawienia_fv >= "' . $vars['dataod'] . ' 00:00:00"';
                $sql_calc .= ' AND fv.data_wystawienia_fv >= "' . $vars['dataod'] . ' 00:00:00"';
            }
        }
        if ($vars['datado'] != '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $vars['datado'])) {
                $datado = $vars['datado'];
                $sql .= ' AND fv.data_wystawienia_fv <= "' . $vars['datado'] . ' 23:59:59"';
                $sql_calc .= ' AND fv.data_wystawienia_fv <= "' . $vars['datado'] . ' 23:59:59"';
            }
        }
        if ((int)$vars['partner'] !== 0 and $this->partner['partner']['partnertype'] == 2) {
            $partner = (int)$vars['partner'];
            $sql .= ' AND fv.installation = ' . $partner;
            $sql_calc .= ' AND fv.installation = ' . $partner;
        }

        if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
            $ss = $this->sdb->ESCAPE($vars['sSearch']);
            $sql .= ' AND (fv.sprzedawca LIKE "%' . $ss . '%" OR '
                . 'fv.nabywca LIKE "%' . $ss . '%" OR fv.numer_fv LIKE "%' . $ss . '%" OR us.email LIKE "%' . $ss . '%" OR fv.id_zamowienia LIKE "%' . $ss . '%" OR fv.nip LIKE "%' . $ss . '%")';
            $sql_calc .= ' AND (fv.sprzedawca LIKE "%' . $ss . '%" OR '
                . 'fv.nabywca LIKE "%' . $ss . '%" OR fv.numer_fv LIKE "%' . $ss . '%" OR us.email LIKE "%' . $ss . '%" OR fv.id_zamowienia LIKE "%' . $ss . '%" OR fv.nip LIKE "%' . $ss . '%")';
        }

        if (isset($vars['iSortCol_0'])) {
            for ($i = 0; $i < (int)$vars['iSortingCols']; $i++) {
                if ($vars['bSortable_' . (int)$vars['iSortCol_' . $i]] === "true") {
                    $sql .= ' ORDER BY ' . $columny[(int)$vars['iSortCol_' . $i]] . ' ' . ($vars['sSortDir_' . $i] === 'asc' ? 'ASC' : 'DESC');
                    $sql_calc .= ' ORDER BY ' . $columny[(int)$vars['iSortCol_' . $i]] . ' ' . ($vars['sSortDir_' . $i] === 'asc' ? 'ASC' : 'DESC');
                }
            }
        }

        $sql .= ' LIMIT ' . ($s - 1) * $strcount . ', ' . $strcount;
        $dane = $this->sdb->select($sql);
        if (false === is_array($dane)) {
            $dane = array();
        }
        $fullrows = $this->sdb->select_r($sql_calc);
        $totalall = $fullrows['ile'];
        $output = array(
            "sEcho" => (int)$vars['sEcho'],
            "iTotalRecords" => $totalall,
            "iTotalDisplayRecords" => $totalall,
            "aaData" => array()
        );
        foreach ($dane as &$v) {
            $link = App::GenerateLink('faktura', $v['id']);
            $output['aaData'][] = array(
                $v['sprzedawca'],
                $v['numer_fv'],
                $v['nabywca'],
                $v['email'],
                $v['data_wystawienia_fv'],
                $v['nip'] ?? '',
                $v['kwota_fv'],
                $v['id_zamowienia'],
                '<a class="btn btn-success" href="' . $link . '">PDF</a>'
            );
        }

        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode($output));
    }

    public function FakturowniaOsrodka()
    {
        if ((int)$this->partner['partner']['partnertype'] === 2) {
            $franczyzowi = $this->sdb->select_r("SELECT value FROM " . TABLE_PREFIX . "partners_meta WHERE id_parent =" . $this->partner['partner']['id'] . " AND properties = 'partner_partner_franczyzowy'");
            $partnerzyFranczyzowi = $this->sdb->select("SELECT id, shortname FROM " . TABLE_PREFIX . "partners WHERE `id` IN (" . $franczyzowi['value'] . ")");
            $this->app->ADD('partnerzywp', $partnerzyFranczyzowi ?? []);
        }

        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/fakturowniaLista.tpl");
        $this->tpl_data['page'] = 'fakturownia';
        $this->app->ADD('params', ['page' => 'fakturownia']);
        $this->Display('faktury', 'Lista fakturownia');
    }


    public function FakturowniaOsrodkaDt()
    {
        $partnerId = $this->partner['partner']['id'];
        $vars = $_GET;
        $s = 1;
        $strcount = 10;
        if (isset($vars['iDisplayStart']) && (int)$vars['iDisplayLength'] !== -1) {
            $s = floor((int)$vars['iDisplayStart'] / (int)$vars['iDisplayLength']) + 1;
            $strcount = (int)$vars['iDisplayLength'];
        }
        if ($s === 0) {
            $s = 1;
        }
        if ($strcount === 0) {
            $strcount = 10;
        }

        $columny = [
            'fv.numer_fv',
            'fv.nabywca',
            'us.email',
            'fv.data',
            'fv.kwota_fv',
            'fv.id_zamowienia',
        ];

        $partnerFranczyzowiSQL = "SELECT value FROM " . TABLE_PREFIX . "partners_meta WHERE id_parent = " . $partnerId . " AND properties= 'partner_partner_franczyzowy' AND current=1";
        $partnerFranczyzowy = $this->sdb->select_r($partnerFranczyzowiSQL)['value'];

        if (!empty($partnerFranczyzowy)) {
            $fas = new \App\Services\FakturowniaService($this->app);
            $fas->setFakturowniaData($partnerFranczyzowy);
            $department_id = $fas->_fa_fakturownia['fakturowniaODDZIAL'];
//            $sql = "SELECT fv.*, fv.id as ido, fv.email, zam.id FROM " . TABLE_PREFIX . "fakturownia fv"
//                . ' LEFT JOIN '.TABLE_PREFIX.'zamowienia zam ON zam.id=fv.id_zamowienia'
//                . ' LEFT JOIN '.TABLE_PREFIX.'uzytkownik us ON us.id=zam.id_usera'
//                . ' WHERE fv.installation IN ('. $partnerFranczyzowy .','.$partnerId. ')';

            $sql = "SELECT fv.*, fv.id as ido, fv.email, zam.id FROM " . TABLE_PREFIX . "fakturownia fv"
                . ' LEFT JOIN ' . TABLE_PREFIX . 'zamowienia zam ON zam.id=fv.id_zamowienia'
                . ' LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik us ON us.id=zam.id_usera'
                . ' WHERE fv.department_id = ' . $department_id;
        } else {
            $sql = "SELECT fv.*, fv.id as ido, fv.email, zam.id FROM " . TABLE_PREFIX . "fakturownia"
                . ' LEFT JOIN ' . TABLE_PREFIX . 'zamowienia zam ON zam.id=fv.id_zamowienia '
                . ' LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik us ON us.id=zam.id_usera'
                . ' WHERE fv.installation = ' . $partnerId;
        }
        if ($vars['dataod'] !== '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $vars['dataod'])) {
                $sql .= ' AND fv.data >= "' . $vars['dataod'] . ' 00:00:00"';
            }
        }
        if ($vars['datado'] !== '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $vars['datado'])) {
                $sql .= ' AND fv.data <= "' . $vars['datado'] . ' 23:59:59"';
            }
        }
        if ((int)$vars['partner'] !== 0 and $this->partner['partner']['partnertype'] == 2) {
            $partner = (int)$vars['partner'];
            $sql .= ' AND fv.installation = ' . $partner;
        }

        if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
            $ss = $this->sdb->ESCAPE($vars['sSearch']);
            $sql .= ' AND ('
                . 'fv.nabywca LIKE "%' . $ss . '%" OR fv.numer_fv LIKE "%' . $ss . '%" OR us.email LIKE "%' . $ss . '%" OR zam.id LIKE "%' . $ss . '%" OR fv.nip LIKE "%' . $ss . '%")';
        }

        if (isset($vars['iSortCol_0'])) {
            for ($i = 0; $i < (int)$vars['iSortingCols']; $i++) {
                if ($vars['bSortable_' . (int)$vars['iSortCol_' . $i]] === "true") {
                    $sql .= ' ORDER BY ' . $columny[(int)$vars['iSortCol_' . $i]] . ' ' . ($vars['sSortDir_' . $i] === 'asc' ? 'ASC' : 'DESC');
                }
            }
        }

        $sql .= ' LIMIT ' . ($s - 1) * $strcount . ', ' . $strcount;
        $dane = $this->sdb->select($sql);
        if (false === is_array($dane)) {
            $dane = array();
        }
        $fullrows = $this->sdb->select_r('SELECT FOUND_ROWS() as ile');
        $totalall = $fullrows['ile'];
        $output = array(
            "sEcho" => (int)$vars['sEcho'],
            "iTotalRecords" => $totalall,
            "iTotalDisplayRecords" => $totalall,
            "aaData" => array()
        );
        foreach ($dane as $v) {
            $link = $v['faktura_link'] . '.pdf';
            $output['aaData'][] = array(
                $v['numer_fv'],
                $v['nabywca'],
                $v['email'],
                $v['data'],
                $v['nip'] ?? '',
                $v['kwota_fv'] / 100,
                $v['id_zamowienia'],
                '<a class="btn btn-success" href="' . $link . '" target="_blank">PDF</a>'
            );
        }

        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode($output));
    }

    public function getZip()
    {
        $partnerId = $this->partner['partner']['id'];
        $filters = $_GET;
        $SQL = "SELECT pdf,numer_fv FROM " . TABLE_PREFIX . "faktury WHERE ";

        $partnerFranczyzowiSQL = "SELECT value FROM " . TABLE_PREFIX . "partners_meta WHERE id_parent = " . $partnerId . " AND properties= 'partner_partner_franczyzowy' AND current=1";
        $partnerFranczyzowy = $this->sdb->select_r($partnerFranczyzowiSQL)['value'];
        $SQL .= "`installation` IN (" . $partnerFranczyzowy . ',' . $this->partner['partner']['id'] . ")";

        if ($filters['dataod'] != '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $filters['dataod'])) {
                $SQL .= ' AND data_wystawienia_fv >= "' . $filters['dataod'] . ' 00:00:00"';
            }
        }
        if ($filters['datado'] != '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $filters['datado'])) {
                $datado = $filters['datado'];
                $SQL .= ' AND data_wystawienia_fv <= "' . $filters['datado'] . ' 23:59:59"';
            }
        }

        $dane = $this->sdb->select($SQL);
        if (false === is_array($dane)) {
            $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/fakturyLista.tpl");
            $this->Display('faktury', 'Lista faktur');
        }
        //create ZIP
        $options = new ZipStream\Option\Archive();
        $options->setSendHttpHeaders(true);
        $zip = new ZipStream\ZipStream('faktury.zip', $options);
        //WHERE TO SAVE
//        $zip->open('faktury.zip', ZipArchive::CREATE == true);
        foreach ($dane as $value) {
            if (file_exists($value['pdf'])) {
                $fname = str_replace('/', '_', $value['numer_fv']);
                $zip->addFile($fname . '.pdf', file_get_contents($value['pdf']));
            }
        }
        $zip->finish();
        die();
    }

    public function FakturyWPLista()
    {
        if ($this->partner['partner']['partnertype'] == 2) {
//            $id = intval($id);
//            $partner = Tools::GetPartnerData(intval($id));
            $partnerzywp = $this->sdb->select('SELECT p.shortname, p.id FROM ' . TABLE_PREFIX . 'partners p WHERE p.id in (SELECT DISTINCT idpartnera FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE idosrodka = ' . $this->partner['partner']['id'] . ')');

//            $sql = 'SELECT DISTINCT rok FROM '.TABLE_PREFIX.'rozliczenia_partner_wp WHERE idpartnera = '.$id;
        } else {
            $id = $this->partner['partner']['id'];
            $partner = $this->partner['partner'];
            $partnerzywp = false;
//            $sql = 'SELECT DISTINCT rok FROM '.TABLE_PREFIX.'rozliczenia_partner_wp WHERE idpartnera = '.$id;
        }


//	$partner = Tools::GetPartnerData(intval($id));

//	$cond = array();
//	if($_GET['dataod'] != "") $cond[] = "ts >= '". date( "Y-m-d", intval(strtotime($_GET['dataod'])) ) ." 00:00:00'";
//	if($_GET['datado'] != "") $cond[] = "ts <= '". date( "Y-m-d", intval(strtotime($_GET['datado'])) ) ." 23:59:59'";
//	$cond[] = "1=1";
//	$fv = $this->sdb->select("SELECT * FROM ".TABLE_PREFIX."rozliczenia_partner_wp WHERE `idpartnera` = ".intval($id)." AND ".implode(" AND ", $cond)." ORDER BY id DESC");
//
//	for($i=0;$i<count($fv);$i++) {
//		$fv[$i]['dataodiso'] = date("Y-m-d", $fv[$i]['dataod']);
//		$fv[$i]['datadoiso'] = date("Y-m-d", $fv[$i]['datado']);
//		$fv[$i]['dataoplacenia'] = ($fv[$i]['dataoplacenia'] > 0 ? date("Y-m-d", $fv[$i]['dataoplacenia']) : "-");
//
//		$fv[$i]['lp'] = $i+1;
//
//		$fv[$i]['netto'] = number_format($fv[$i]['netto']/100, 2);
//		$fv[$i]['brutto'] = number_format($fv[$i]['brutto']/100, 2);
//		$fv[$i]['vat_wart'] = number_format($fv[$i]['vat_wart']/100, 2);
//
//		$fv[$i]['hash'] = md5( 'd5a7r8ar90te3rwaR' . $fv[$i]['id'] . USER::GetUserID() );
//	}
//	$this->app->ADD('fv',$fv);
        $this->app->ADD('partnerzywp', $partnerzywp);
//	$this->app->ADD('partner',$partner);
//	$this->app->ADD('params',$_GET);

        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_faktury_lista.tpl");
        $this->Display('faktury', 'Lista faktur');
    }


    public function AjaxListFakturyWP($vars, $strcount = 10, $s = 1)
    {

        if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
            $s = floor(intval($vars['iDisplayStart']) / intval($vars['iDisplayLength'])) + 1;
            $strcount = intval($vars['iDisplayLength']);
        }
        if ($s == 0) {
            $s = 1;
        }
        if ($strcount == 0) {
            $strcount = 10;
        }


        $columny = array(
            'rwp.id',
            'p.shortname',
            'rwp.nrfaktury',
            'rwp.ts',
            'rwp.id',
            'rwp.brutto',
            'rwp.netto',
            'rwp.vat',
            'v.status'
        );

//        die(var_dump($this->partner));
        if ($this->partner['partner']['partnertype'] == 2) {
            $sql = 'SELECT SQL_CALC_FOUND_ROWS rwp.id, rwp.idpartnera, rwp.idosrodka, FROM_UNIXTIME(rwp.dataod,"%Y-%m-%d") as dataodiso, FROM_UNIXTIME(rwp.datado,"%Y-%m-%d") as datadoiso, '
                . 'rwp.ts, rwp.oplacone, rwp.dataoplacenia, rwp.nrfaktury, rwp.linkfaktury, '
                . 'rwp.brutto, rwp.netto, rwp.vat_wart, p.shortname as partner FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp rwp '
                . 'LEFT JOIN ' . TABLE_PREFIX . 'partners p on rwp.idpartnera = p.id '
                . 'WHERE rwp.idosrodka = ' . $this->partner['partner']['id'];
        } else {
            if ($this->partner['partner']['partnertype'] == 1) {
                $sql = 'SELECT SQL_CALC_FOUND_ROWS rwp.id, rwp.idpartnera, rwp.idosrodka, FROM_UNIXTIME(rwp.dataod,"%Y-%m-%d") as dataodiso, FROM_UNIXTIME(rwp.datado,"%Y-%m-%d") as datadoiso, '
                    . 'rwp.ts, rwp.oplacone, rwp.dataoplacenia, rwp.nrfaktury, rwp.linkfaktury, p.shortname as partner FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp rwp '
                    . 'LEFT JOIN ' . TABLE_PREFIX . 'partners p on rwp.idpartnera = p.id '
                    . 'WHERE rwp.idpartnera = ' . $this->partner['partner']['id'];
            }
        }
        $dataod = false;
        $datado = false;
        $partner = false;

        if ($vars['dataod'] != '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $vars['dataod'])) {
                $dataod = $vars['dataod'];
                $sql .= ' AND rwp.ts > "' . $vars['dataod'] . ' 00:00:00"';
            }
        }
        if ($vars['datado'] != '') {
            if (1 === preg_match('/^\d{4}-\d{2}-\d{2}$/', $vars['dataod'])) {
                $datado = $vars['datado'];
                $sql .= ' AND rwp.ts < "' . $vars['datado'] . ' 23:59:59"';
            }
        }
        if (intval($vars['partner']) !== 0 and $this->partner['partner']['partnertype'] == 2) {
            $partner = intval($vars['partner']);
            $sql .= ' AND rwp.idpartnera = ' . $partner;
        }


        if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
            $ss = $this->sdb->ESCAPE($vars['sSearch']);
            $sql .= ' AND p.shortname LIKE "%' . $ss . '%" OR '
                . 'rwp.nrfaktury LIKE "%' . $ss . '%"';
        }

        if (isset($vars['iSortCol_0'])) {
            for ($i = 0; $i < intval($vars['iSortingCols']); $i++) {
                if ($vars['bSortable_' . intval($vars['iSortCol_' . $i])] == "true") {
                    $sql .= ' ORDER BY ' . $columny[intval($vars['iSortCol_' . $i])] . ' ' . ($vars['sSortDir_' . $i] === 'asc' ? 'ASC' : 'DESC');
                }
            }
        }

        $sql .= ' LIMIT ' . ($s - 1) * $strcount . ', ' . $strcount;
        $dane = $this->sdb->select($sql);
        if (false === is_array($dane)) {
            $dane = array();
        }
        $fullrows = $this->sdb->select_r('SELECT FOUND_ROWS() as ile');
        $totalall = $fullrows['ile'];
        $output = array(
            "sEcho" => intval($vars['sEcho']),
            "iTotalRecords" => $totalall,
            "iTotalDisplayRecords" => $totalall,
            "aaData" => array()
        );

        foreach ($dane as &$v) {
            if ($v['oplacone'] == 0) {
                $status = '<span class="label label-danger">' . $this->app->Lang('Do zapłaty', 'Partner') . '</span>';
                $statusw = 'Do zapłaty';
            } else {
                $status = '<span class="label label-success">' . $this->app->Lang('Zapłacona', 'Partner') . '</span>';
                $statusw = 'Zapłacona';
            }
            if ($this->partner['partner']['partnertype'] == 2) {
                $hash = md5('d5a7r8ar90te3rwaR' . $v['id'] . USER::GetUserID());
                $button = '<button id="updatefv_' . $v['id'] . '" class="btn btn-info updatefv" data-status="' . $statusw . '" data-toggle="modal" data-hash="' . $hash . '" data-target="#FakturaStatusModal" data-id="' . $v['id'] . '" data-nr="' . $v['nrfaktury'] . '"';
                if ($v['oplacone'] == 1) {
                    $button .= ' disabled ';
                }
                $button .= '>' . $this->app->Lang('Zmień', 'Partner') . '</button>';
            } else {
                $button = '';
            }

            if ($v['datadoiso'] == $v['dataodiso']) {
                $rozliczenie = $v['dataodiso'];
            } else {
                if ($v['dataodiso'] > '1970-01-01') {
                    $rozliczenie = 'od ' . $v['dataodiso'] . ' do ' . $v['datadoiso'];
                } else {
                    $rozliczenie = 'do ' . $v['datadoiso'];
                }
            }
            $output['aaData'][] = array(
                $v['id'],
                $v['partner'],
                $v['nrfaktury'],
                $v['ts'],
                $rozliczenie,
                round($v['brutto'] / 100, 2),
                round($v['netto'] / 100, 2),
//                round($v['vat_wart']/100,2),
                $status,
                $v['dataoplacenia'] != 0 ? date('Y-m-d', $v['dataoplacenia']) : 'brak',
                $button . '<a href="' . $v['linkfaktury'] . '" target="_blank" class="btn btn-default">' . $this->app->Lang('Pobierz', 'Partner') . '</a>'
            );
        }

        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode($output));
    }

    /**
     * List rozliczeń Partner Ośrodek - Partner WP
     */
    public function Rozliczeniawp($id = false)
    {
        if ($this->partner['partner']['partnertype'] == 2) {
            $id = intval($id);
            $sql = 'SELECT DISTINCT rok FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE idpartnera = ' . $id;
        } else {
            $id = $this->partner['partner']['id'];
            $sql = 'SELECT DISTINCT rok FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE idpartnera = ' . $id;
        }
        $roczniki = array();
        $this->sdb->query($sql);
        while ($r = $this->sdb->fetch_row()) {
            $roczniki[] = $r['rok'];
        }
        if (count($roczniki) == 0) {
            $roczniki = array(date('Y'));
        }
        $params['roczniki'] = $roczniki;
        $params['id'] = $id;
        $params['cmonth'] = date('n');
        $params['crozliczenia'] = $this->sdb->select('SELECT id, Date_FORMAT(FROM_UNIXTIME(dataod),"%Y-%m-%d") as dataod, DATE_FORMAT(FROM_UNIXTIME(datado),"%Y-%m-%d") as datado FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE idpartnera=' . $id . ' AND miesiac = ' . $params['cmonth'] . ' ORDER BY dataod DESC');
        $partner = Tools::GetPartnerData($id);
        $this->app->ADD('partner', $partner);
        $this->app->ADD('params', $params);
        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_rozliczeniawp.tpl");
        $this->Display('rozliczeniawp', 'Lista rozliczeń');
    }

    public function GetRozliczenieWP($id)
    {

    }

    public function ListaRozliczeniaWP()
    {
        $pid = intval($_POST['id']);
        $mc = intval($_POST['miesiac']);
        $rok = intval($_POST['rok']);
        if (0 === $pid || 0 === $mc || 0 === $rok) {
            return $this->AjaxReturnError('Błędne dane!');
        }
        $sql = 'SELECT id, rok, miesiac, dzien, dataod, datado, akceptacja FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE idpartnera=' . $pid . ' AND rok=' . $rok . ' AND miesiac = ' . $mc . ' ORDER BY dataod DESC';
        $r = $this->sdb->query($sql);
        $row = array();
        while ($r = $this->sdb->fetch_row()) {
            $row[] = array(
                'id' => $r['id'],
                'rok' => $r['rok'],
                'miesiac' => $r['miesiac'],
                'dzien' => $r['dzien'],
                'od' => date('Y-m-d', $r['dataod']),
                'do' => date('Y-m-d', $r['datado']),
                'akceptacja' => $r['akceptacja']
            );
        }
        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode(array('data' => $row)));
        return;
    }

    /**
     * Panel statystyk śnieżynek
     */
    public function Statystyki()
    {
        switch ($_GET['b']) {
            case 'sniezynki':
                $fla = array();
                $rano = strtotime(date('Y-m-d') . '00:00:01');
                $teraz = time();
                $sezon = intval(date(m)) < 9 ? date('Y') - 1 : date('Y');
                $sezon_start = strtotime($sezon . '-09-01 00:00:01');
                $do = new DateTime();
                $do->sub(new DateInterval('P' . (date('N') - 1) . 'D'));
                $ptygodnia = strtotime($do->format('Y-m-d') . '00:00:01');
                $pmiesiaca = strtotime(date('Y-m' . '-01 00:00:01'));
                $mgo = Mymongo::activate();
                $sne = $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'pieczatki_emisje WHERE emitent = ' . INSTALLATION . ' AND status = 1');
                if (false === is_array($sne)) {
                    $sne = array();
                }
                foreach ($sne as $v) {
                    $oferty[] = $v['id'];
                    $of = $mgo->mget_offer_details($v['oferta']);
                    $fla[$v['id']]['oferta'] = $of['nazwa'][LANG];
                    $sn = $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'pieczatki WHERE id_emisji = ' . $v['id']);
                    foreach ($sn as $w) {
                        if ($w['status'] == 1) {
                            $fla[$v['id']]['ilosc']++;
                            $fla[$v['id']]['users'][$w['id_usera']]['ilosc']++;
                        }
                        if ($w['status'] == 2) {
                            $fla[$v['id']]['przeterminowane']['ilosc']++;
                        }
                    }
                    $kp = $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'kupony WHERE id_emisji = ' . $v['id_emisji']);
                    foreach ($kp as $k) {
                        $fla[$v['id']]['kupony']['wygenerowane']['ilosc']++;
                        if (intval($k['uzycie']) == 1) {
                            $fla[$v['id']]['kupony']['uzyte']['ilosc']++;
                            if (strtotime($k['data_uzycia']) > $sezon_start) {
                                $fla[$v['id']]['daty']['sezon']['wartosc'] += $k['wartosc_uzycia_brutto'];
                                $fla[$v['id']]['daty']['razem']['wartosc'] += $k['wartosc_uzycia_brutto'];
                                $fla[$v['id']]['daty']['sezon']['ilosc']++;
                                $fla[$v['id']]['daty']['razem']['ilosc']++;
                            }
                            if (strtotime($k['data_uzycia']) > $pmiesiaca) {
                                $fla[$v['id']]['daty']['miesiac']['wartosc'] += $k['wartosc_uzycia_brutto'];
//                            $fla[$v['id']]['daty']['razem']['wartosc'] += $k['wartosc_uzycia_brutto'];
                                $fla[$v['id']]['daty']['miesiac']['ilosc']++;
//                            $fla[$v['id']]['daty']['razem']['ilosc']++;
                            }
                            if (strtotime($k['data_uzycia']) > $ptygodnia) {
                                $fla[$v['id']]['daty']['tydzien']['wartosc'] += $k['wartosc_uzycia_brutto'];
                                $fla[$v['id']]['daty']['tydzien']['ilosc']++;
                            }
                            if (strtotime($w['data_uzycia']) > $rano) {
                                $fla[$v['id']]['daty']['dzisiaj']['wartosc'] += $k['wartosc_uzycia_brutto'];
                                $fla[$v['id']]['daty']['dzisiaj']['ilosc']++;
                            }
                        }
                    }

//                    $fla[$v['id']]['users']['ilosc'] = count($fla[$v['id']]['users']);
                    if (false == is_array($fla[$v['id']]['users'])) {
                        $fla[$v['id']]['users'] = array();
                    }
                    foreach ($fla[$v['id']]['users'] as $u) {
                        $fla[$v['id']]['users']['ilosc']++;
                        $fla[$v['id']]['ilosci'][$u['ilosc']]['ilosc']++;
                    }
                    $fla[$v['id']]['iloscp'] = $v['ilosc_pieczatek'];
                    $fla[$v['id']]['wartoscp'] = $v['wartosc_pieczatki'];
                    $fla[$v['id']]['czasp'] = $v['data_waznosci_pieczatki'];
                    $fla[$v['id']]['czask'] = $v['data_waznosci_kuponu'];
                    $sk = explode('|', $v['koszty']);
                    $fla[$v['id']]['koszty'] = $sk[0];
                }

                $dat = new DateTime(date('Y-m-d') . ' 00:00:01');
                $dat->sub(new DateInterval('P6M'));
                $dataod = $dat->format('Y-m-d H:i:s');
                if (!is_array($oferty)) {
                    $oferty = array(0);
                }
                $sql = "SELECT ROUND(SUM(brutto)/100,2) as brutto, DATE_FORMAT(ts,'%Y-%m-%d') as pdata, COUNT(id) as id "
                    . "FROM ts_pieczatki WHERE ts > '" . $dataod . "' AND id_emisji in(" . join(',',
                        $oferty) . ") group by DATE_FORMAT(ts,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wdata = array();
                while ($r = $this->sdb->fetch_row()) {
                    $wdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $r['brutto'] . '}';
//                    $wdata[] = '{"date": "'.$r['pdata'].'","value": '.$r['brutto'].'","value2" : '.$r['id'].'}';
                }
                $this->pfooter_data['wykres'] = 'sn';
                $this->pfooter_data['wykres_data'] = join(',' . PHP_EOL, $wdata);

//                die(var_dump($fla));
                $this->app->ADD('wykres_tytul', "Wartość zbieranych śnieżynek każdego dnia");
                $this->app->ADD('sniezynki', $fla);
                $this->app->ADD('sezon', $sezon);
                $this->Display('sniezynki', 'Śnieżynki');

                break;

            case 'kupony':
                $fla = array();
                $rano = strtotime(date('Y-m-d') . '00:00:01');
                $teraz = time();
                $sezon = intval(date(m)) < 9 ? date('Y') - 1 : date('Y');
//                $sezon_start = strtotime('2014-09-01 00:00:01');
                $sezon_start = strtotime($sezon . '-09-01 00:00:01');
                $do = new DateTime();
                $do->sub(new DateInterval('P' . (date('N') - 1) . 'D'));
                $ptygodnia = strtotime($do->format('Y-m-d') . '00:00:01');
                $pmiesiaca = strtotime(date('Y-m' . '-01 00:00:01'));
                $mgo = Mymongo::activate();
                $sql = 'SELECT ke.*, kel.value as nazwaemisji, kep.udzial_w_platnosci '
                    . 'FROM ' . TABLE_PREFIX . 'kupony_emisje ke '
                    . 'JOIN ' . TABLE_PREFIX . 'kupony_emisje_langs kel ON kel.parentid = ke.id '
                    . 'JOIN ' . TABLE_PREFIX . 'kupony_platnosc kep ON ke.id = kep.id_emisji '
                    . 'WHERE kel.langid = ' . LANG . ' AND kel.property="nazwa" AND kep.id_partnera = ' . INSTALLATION . ' AND ke.emitent = ' . INSTALLATION . ' '
                    . 'AND ke.status = 1 AND rodzaj_kuponu <> "USER->PREPAID"';
                $sne = $this->sdb->select($sql);
                if (false === is_array($sne)) {
                    $sne = array();
                }
                foreach ($sne as $v) {
                    $oferty[] = $v['id'];
                    $sql_o = 'SELECT oferta FROM ' . TABLE_PREFIX . 'kupony_emisje_oferty WHERE emisja = ' . $v['id'];
                    $offersids = $this->sdb->select($sql_o);
                    if (false == is_array($offersids)) {
                        $offersids = array();
                    }
                    foreach ($offersids as $ofid) {
                        $of = $mgo->mget_offer_details($ofid['oferta']);
                        $nazwyofert[] = $of['nazwa'][LANG];
                    }
                    $fla[$v['id']]['nazwaemisji'] = $v['nazwaemisji'];
                    $fla[$v['id']]['oferta'] = join(', ', $nazwyofert);
                    $fla[$v['id']]['dataod'] = $v['data_waznosci_od'];
                    $fla[$v['id']]['datado'] = $v['data_waznosci_do'];
                    if ($v['przeznaczenie_kuponu'] == 'TYP-PRODUKTU') {
                        $fla[$v['id']]['przeznaczenie'] = $v['produkt'];
                    }
                    if ($v['przeznaczenie_kuponu'] == 'PRODUKT') {
                        switch ($v['typ_produktu']) {
                            case 'KARNET':
                                $produkt = $mgo->mget_product_details($v['produkt'], 'karnety');
                                break;
                            case 'KARTA':
                                $produkt = $mgo->mget_product_details($v['produkt'], 'karty');
                                break;
                            case 'UBEZPIECZENIE':
                                $produkt = $mgo->mget_product_details($v['produkt'], 'ubezpieczenia');
                                break;
                            case 'VOUCHER':
                                $produkt = $mgo->mget_product_details($v['produkt'], 'vouchery');
                                break;
                            case 'VPRODUKT':
                                $produkt = $mgo->mget_product_details($v['produkt'], 'produkty_wirtualne');
                                break;
                        }

                        $fla[$v['id']]['przeznaczenie'] = $v['typ_produktu'] . '<br>' . $produkt['nazwa'][LANG];
                    }
                    $fla[$v['id']]['typemisji'] = $v['rodzaj_kuponu'];
                    $fla[$v['id']]['udzial'] = $v['udzial_w_platnosci'];
                    $fla[$v['id']]['wielkoscemisji'] = $v['wielkosc_emisji'];
                    $fla[$v['id']]['wartosckuponu'] = $v['wartosc_jedn_kuponu'];
                    $fla[$v['id']]['wartoscemisji'] = number_format($v['wartosc_jedn_kuponu'] * $v['wielkosc_emisji'], 2);
                    $sql_w = 'SELECT count(id) as ilosc, round(sum(wartosc_uzycia_brutto),2) as wartosc FROM ' . TABLE_PREFIX . 'kupony WHERE uzycie = 1 AND id_emisji = ' . $v['id'];
                    $dd = $this->sdb->select($sql_w);
                    $fla[$v['id']]['iloscuzytychkuponow'] = intval($dd[0]['ilosc']);
                    $fla[$v['id']]['wartoscuzytychkuponow'] = number_format($dd[0]['wartosc'], 2);
                    $fla[$v['id']]['kosztpartnera'] = number_format($dd[0]['wartosc'] * $v['udzial_w_platnosci'] / 100, 2);
                    $kp = $this->sdb->select('SELECT * FROM ' . TABLE_PREFIX . 'kupony WHERE uzycie = 1 AND id_emisji = ' . $v['id']);
                    foreach ($kp as $k) {
                        $fla[$v['id']]['kupony']['wygenerowane']['ilosc']++;
                        $fla[$v['id']]['kupony']['uzyte']['ilosc']++;
                        if (strtotime($k['data_uzycia']) > $sezon_start) {
                            $fla[$v['id']]['daty']['sezon']['wartosc'] += $k['wartosc_uzycia_brutto'];
                            $fla[$v['id']]['daty']['razem']['wartosc'] += $k['wartosc_uzycia_brutto'];
                            $fla[$v['id']]['daty']['sezon']['ilosc']++;
                            $fla[$v['id']]['daty']['razem']['ilosc']++;
                        }
                        if (strtotime($k['data_uzycia']) > $pmiesiaca) {
                            $fla[$v['id']]['daty']['miesiac']['wartosc'] += $k['wartosc_uzycia_brutto'];
                            $fla[$v['id']]['daty']['miesiac']['ilosc']++;
                        }
                        if (strtotime($k['data_uzycia']) > $ptygodnia) {
                            $fla[$v['id']]['daty']['tydzien']['wartosc'] += $k['wartosc_uzycia_brutto'];
                            $fla[$v['id']]['daty']['tydzien']['ilosc']++;
                        }
                        if (strtotime($w['data_uzycia']) > $rano) {
                            $fla[$v['id']]['daty']['dzisiaj']['wartosc'] += $k['wartosc_uzycia_brutto'];
                            $fla[$v['id']]['daty']['dzisiaj']['ilosc']++;
                        }
                    }

                    unset($nazwyofert);
                }
//                die(var_export($fla));
                $dat = new DateTime(date('Y-m-d') . ' 00:00:01');
                $dat->sub(new DateInterval('P6M'));
                $dataod = $dat->format('Y-m-d H:i:s');
                if (!is_array($oferty)) {
                    $oferty = array(0);
                }
                $sql = "SELECT ROUND(SUM(wartosc_uzycia_brutto)/100,2) as brutto, DATE_FORMAT(ts,'%Y-%m-%d') as pdata, COUNT(id) as id "
                    . "FROM ts_kupony WHERE ts > '" . $dataod . "' AND id_emisji in(" . join(',',
                        $oferty) . ") group by DATE_FORMAT(ts,'%Y-%m-%d') order by pdata";
                $this->sdb->query($sql);
                $wdata = array();
                while ($r = $this->sdb->fetch_row()) {
                    $wdata[] = '{"date": "' . $r['pdata'] . '","value": ' . $r['brutto'] . '}';
//                    $wdata[] = '{"date": "'.$r['pdata'].'","value": '.$r['brutto'].'","value2" : '.$r['id'].'}';
                }
                $this->pfooter_data['wykres'] = 'sn';
                $this->pfooter_data['wykres_data'] = join(',' . PHP_EOL, $wdata);

//                die(var_dump($fla));
                $this->app->ADD('wykres_tytul', "Wartość użytych kuponó każdego dnia");
                $this->app->ADD('kupony', $fla);
                $this->app->ADD('sezon', $sezon);
                $this->Display('kupony', 'Kupony - statystyki');
                break;
        }

    }

    private function Display($tpl, $title = 'Panel partnera')
    {
        $user_full_name = User::GetUserFullName() ?: 'Noname';
        $partner = $this->partner;
        $this->dash_data['full_name'] = $user_full_name;
        $this->ptop_data['full_name'] = $user_full_name;
        $this->ptop_data['user'] = User::GetUserData();
        $this->dash_data['partner'] = $partner;
        $this->ptop_data['partner'] = $partner;
        $this->header_data['title'] = $title;
        $this->app->AddWidgetsData('pheader', $this->header_data);
        $this->app->AddWidgetsData('pdashboard', $this->dash_data);
        $this->app->AddWidgetsData('ptopnav', $this->ptop_data);
        $this->app->AddWidgetsData('pfooter', $this->pfooter_data);
        $this->app->AddWidgetsData('palerts', $this->alerts);
        $this->app->AddWidgetsData('pbasket', '');
        $this->app->AddDecoratorData('data', $this->tpl_data);
        $this->app->SetTemplate('modules/partner/' . $tpl . '.tpl');
        $this->alerts = array();
    }

    static function getPSPList()
    {
        $mgo = Mymongo::activate();
        $db = Database::activate();
        $where['kupon_emitent'] = (string)INSTALLATION;
        $where['$or'] = array(
            array('status.id' => 5),
            array('status.id' => 6),
            array('status.id' => 7)
        );
        $proj = [
            'projection' => [
                'partnerzy_sp' => 1
            ]
        ];

        $cursor = $mgo->db->offers->find($where, $proj);
        $ids = array();
        foreach ($cursor as $v) {
            $r = $mgo->Mgo2array($v);
            foreach ($r['partnerzy_sp'] as $p) {
                $ids[$p['id']] = 1;
            }
        }
        return array_keys($ids);
    }

    public function filterWp()
    {
        $sql = 'SELECT partner FROM ' . TABLE_PREFIX . 'wpconfig WHERE osrodek=' . INSTALLATION;
        $results = $this->sdb->query($sql);
        if (!is_array($results)) {
            $results = [];
        }
        return $results;
    }

    static function getPOList()
    {
        $mgo = Mymongo::activate();
        $db = Database::activate();
        $where['partnerzy_sp.id'] = INSTALLATION;
        $where['$or'] = array(
            array('status.id' => 5),
            array('status.id' => 6),
            array('status.id' => 7)
        );
        $proj = [
            'projection' => [
                'kupon_emitent' => 1
            ]
        ];

        $cursor = $mgo->db->offers->find($where, $proj);
        $ids = array();
        foreach ($cursor as $v) {
            $r = $mgo->Mgo2array($v);
            $ids[$r['kupon_emitent']] = 1;
        }
        return array_keys($ids);
    }


    public function AjaxChargeWallet()
    {
        $post = $_POST;
        $kwota = round(floatval($post['kwota']), 2);
        $partner = intval($post['partner']);
        $ret = Wallet::ChargeWallet($kwota, true, $partner);
        if (true === $ret) {
            $ballance = Wallet::StaticCheckBalance($partner);
            $ballance_formated = number_format(($ballance / 100), 2);
            echo json_encode(array(
                'status' => 'OK',
                'ballance' => $ballance,
                'ballance_formated' => $ballance_formated,
                'partner' => $partner,
                'kwota' => $kwota
            ));
        } else {
            echo json_encode(array('status' => 'false'));
        }
        die();
    }


    public function AjaxRozliczPartneraWP()
    {
        require_once 'application/admin_v4_0/modules/finance/reportsOsrodek.php';
        $mid = intval($_GET['partner']);
//        $day = '2017-04-05';
        $day = $this->sdb->Escape($_GET['day']);
        $roz = Reports4Osrodek::tabelaRozliczZamowieniaWP($mid, $day);
        if ($roz['status'] == 'OK') {
            $faktura = self::_prepareInvoiceWP($roz['mgoid']);
            $sql = 'UPDATE ' . TABLE_PREFIX . 'rozliczenia_partner_wp SET nrfaktury ="' . $this->sdb->Escape($faktura['nrfaktury']) . '", linkfaktury = "/' . $this->sdb->Escape($faktura['plik']) . '" WHERE id = ' . $roz['id'];
            $this->sdb->query($sql);
            $ret['status'] = 'OK';
            $ret['linkfaktury'] = App::GenerateLink('rozliczenia_partner_wp', $roz['id']);
            $ret['nrfaktury'] = $faktura['nrfaktury'];
            echo json_encode($ret);
            die();
        }
        $ret['status'] = 'error';
        $ret['error'] = $roz['error'];
        echo json_encode($ret);
        die();
    }

    public function ajaxUpdateFaktura()
    {
        $post = $_POST;
        $hash = md5('d5a7r8ar90te3rwaR' . intval($post['fvid']) . USER::GetUserID());
        if ($post['hash'] == $hash) {
            $sql = "UPDATE " . TABLE_PREFIX . "rozliczenia_partner_wp SET `oplacone`=" . intval($post['nowystatus']) . ", `dataoplacenia`=" . (strtotime($post['datazaplaty'])) . ", `useroplacenia`=" . intval(USER::GetUserID()) . " WHERE id = " . intval($post['fvid']) . " LIMIT 1";
            $res = $this->sdb->query($sql);
            $return = array();
            if ($res) {
                $return['status'] = 'OK';
                $return['nowystatus'] = intval($post['nowystatus']);
                $return['datazaplaty'] = $post['datazaplaty'];
                $return['id'] = intval($post['fvid']);

                if (intval($post['nowystatus']) == 1 && $post['topup'] === 'true') {
                    $rozliczenie = $this->sdb->select_r("SELECT * FROM " . TABLE_PREFIX . "rozliczenia_partner_wp WHERE id=" . intval($post['fvid']));
                    Wallet::ChargeWallet($rozliczenie['brutto'] / 100, true, $rozliczenie['idpartnera']);
                }
            } else {
                $return['status'] = 'error';
            }
        } else {
            $return['status'] = 'error';
        }
        echo json_encode($return);
        die();
    }

    public function historiawp()
    {
        if ($this->partner['partner']['partnertype'] == 2) {
            $partnerzywp = $this->sdb->select('SELECT p.shortname, p.id FROM ' . TABLE_PREFIX . 'partners p WHERE p.id in (SELECT DISTINCT idpartnera FROM ' . TABLE_PREFIX . 'rozliczenia_partner_wp WHERE idosrodka = ' . $this->partner['partner']['id'] . ')');
        } else {
            $partnerzywp = false;
        }
        $postback = $this->app->postparam;
        if ($_GET['datado'] == '') {
            $params['datado'] = date('Y-m-d');
        } else {
            $params['datado'] = $_GET['datado'];
        }
        if ($_GET['dataod'] != '') {
            $params['dataod'] = $_GET['dataod'];
        }
        $params['pid'] = intval($_GET['partnersp']);
        $partner = Tools::GetPartnerData($params['pid']);
        $this->app->ADD('params', $params);
        $this->app->ADD('partner', $partner);
        $this->app->ADD('partnerzywp', $partnerzywp);
        $this->app->AddDecoratorModule('moduledata', "modules/" . MODULE . "/raports/raport_historiawp.tpl");
        $this->Display('historiawp', 'Historia transakcji partnera');
    }


    /**
     * Inicjacja sprawdzania, czy bilet był użyty danego dnia (ajax)
     * @param int $karta_id ID karty
     * @param string $data 'YYYY-MM-DD'
     */
    public function CheckTicketUse($karta_id, $serial, $data)
    {
        if (false === $karta_id) {
            $karta_id = $this->sdb->get_field_val('karty', 'id', array(array('serial', $serial, 'STRING')));
        }
        $karta_id = intval($karta_id);
        $pat = '/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i';
        if ($karta_id != 0 and preg_match($pat, $data, $macz) and checkdate($macz[2], $macz[3], $macz[1])) {
            $this->app->SetDecorator('Ajax');
            $this->app->ADD('response', json_encode($this->DidHeSki($karta_id, $macz[0], $serial, true)));
        } else {
            $this->app->SetDecorator('Ajax');
            $this->app->ADD('response', json_encode(array('status' => "error", 'code' => "Brak karty o tym numerze")));
        }
    }

    public function AjaxCheckCardUse($vars, $strcount = 10, $s = 1)
    {
        if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
            $s = floor(intval($vars['iDisplayStart']) / intval($vars['iDisplayLength'])) + 1;
            $strcount = intval($vars['iDisplayLength']);
        }
        if ($s == 0) {
            $s = 1;
        }
        if ($strcount == 0) {
            $strcount = 10;
        }
        $karta = $vars['serial'];
        $karta = ltrim($karta, '0');
        if (0 === preg_match('/\d*/', $karta)) {
            return 0;
        }
//                $where[] = array('answer.HISTORIA.KARTA'=>$karta);
//                $where[] = array('answer.HISTORIA.TYPREJ_I'=>'49');
        $where = array(
            'answer.HISTORIA' =>
                array(
                    '$elemMatch' =>
                        array(
                            'KARTA' => $karta,
                            'TYPREJ_I' => '49'
                        )
                )
        );
        $proj = array('answer.HISTORIA.$' => 1);
        $mgo = Mymongo::activate();
        $cursor = $mgo->db->apibs_zuzycie->find($where, $proj);
        $l = 1;
        $zz = array();
        foreach ($cursor as $v) {
            $row = $mgo->Mgo2array($v);
            $zz[] = array($l, date('Y-m-d', strtotime($row['answer']['HISTORIA'][0]['DATA'])));
            $l++;
        }

        $output = array(
            "sEcho" => intval($vars['sEcho']),
            "iTotalRecords" => count($zz),
            "iTotalDisplayRecords" => count($zz),
            "aaData" => $zz
        );
        echo json_encode($output);
        die();
    }

    /**
     * Sprawdzenie, czy bilet był używany
     * @param int $karta_id
     * @param string $data
     * @param bool $admin_mode
     * @return string
     */
    public function DidHeSki($karta_id, $data, $serial, $admin_mode = false)
    {
        $this->app->SetDecorator('Ajax');
        if (!$admin_mode) {
            $cancheck = $this->sdb->get_field_val('karnety', 'id_karty',
                array(array('id_karty', $karta_id, 'INT'), array('id_usera', User::GetUserID(), 'INT')));
            if ($cancheck === false) {
                $resp['status'] = 'error';
                $resp['code'] = '-200';
                return $resp;
            }
        }
        $sql = 'SELECT kz.* FROM ' . TABLE_PREFIX . 'karnety_zuzycie kz ' .
            'WHERE kz.id_karty = ' . $karta_id . ' AND kz.data between "' . $data . ' 00:00:01" AND "' . $data . ' 23:59:59"';
//        $sql = 'SELECT k.id, k.status, kz.zuzycie, kz.id_bramki, kz.data FROM ' . TABLE_PREFIX . 'karnety k, ' . TABLE_PREFIX . 'karnety_zuzycie kz ' .
//                'WHERE k.id=kz.id_karnetu AND k.id=' . $karta_id . ' AND kz.data between "' . $data . ' 00:00:01" AND "' . $data . ' 23:59:59"';
        $status = $this->sdb->select($sql);
        $resp['status'] = '-1';
        if (count($status) > 0) {
            foreach ($status as $v) {
                if ($v['zuzycie'] == '1') {
                    $resp['status'] = '1';
                }
                if ($v['zuzycie'] == '0') {
                    $resp['status'] = '0';
                    break;
                }
            }
        } else {
            $resp['status'] = 'error';
            $resp['code'] = 'No ticket on this day';
        }

        return $resp;
    }

    public static function sendInvoiceEmail($okres, $mid, $copy = true, array $attachments = [])
    {
        $en = Database::activate();
        $settings = Tools::GetSystemSettings();
        $params[md5('%SETTLE_PERIOD%')] = $okres;
        $params[md5('%SITE_PARTNERURL%')] = $settings['partnerUrl']['value'];
        $params[md5('%SITE_EMAIL%')] = $settings['bokEmail']['value'];
        $params['files'] = $attachments;
        $sql = 'SELECT u.* '
            . 'FROM ' . TABLE_PREFIX . 'uzytkownik u, ' . TABLE_PREFIX . 'partner_user pu '
            . 'WHERE u.id = pu.iduser AND pu.idpartner = ' . $mid . ' AND u.uprawnienia = 3';
        $users = $en->select($sql);
        if (!is_array($users)) {
            $users = [];
        }
        foreach ($users as $user) {
            $params['lang'] = $user['defaultlang'];
            $params['email'] = $user['email'];
            $params[md5('%USER_FNAME%')] = $user['imie'] . ' ' . $user['nazwisko'];
            Mailsend::SendEmail('23_invoice_resort', $params, $settings['automatEmail']['value']);
        }
        if ($copy) {
            $params['email'] = $settings['bokEmail']['value'];
            $params['lang'] = 24;
            Mailsend::SendEmail('23_invoice_resort', $params, $settings['automatEmail']['value']);
        }
    }

    public function StanKarty()
    {

        switch ($this->partner['partner']['partnertype']) {
            case 1:
                $serv = new KartyDT();
                $karty = $serv->KartyPartnerSprzedaz();
                $this->tpl_data = array('karty' => $karty);
                if ($_GET['s']) {
                    $this->tpl_data['dtfilter'] = $_GET['s'];
                }
                $this->Display('karty-psp', 'Lista kart partnera');
                break;
            case 2:
                $this->Display('karty-osrodek', 'Lista kart partnera');
                break;
            default:
                $this->sdb->query('SELECT iduser FROM ' . TABLE_PREFIX . 'partner_user WHERE idpartner = ' . INSTALLATION);
                while ($r = $this->sdb->fetch_row()) {
                    $users[] = $r['iduser'];
                }
                if (count($users) === 0) {
                    return;
                }
                $users = join(',', $users);

                $this->sdb->query('SELECT id,serial FROM ' . TABLE_PREFIX . 'karty WHERE id_usera in(' . $users . ') AND status > -1');
                while ($re = $this->sdb->fetch_row()) {
                    $karty[$re['serial']]['id'] = $re['id'];
                    $karty[$re['serial']]['numer_zamowienia'] = '-';
                    $karty[$re['serial']]['liczba_dni'] = '-';
                    $karty[$re['serial']]['ciagly'] = '-';
                    $karty[$re['serial']]['status'] = '-';
                    $karty[$re['serial']]['ile'] = '-';
                }

                $sql2 = 'SELECT kt.serial_karty, kt.liczba_dni, kt.ciagly, kt.id_zamowienia,kt.status, kz.dni as ile FROM ts_karnety kt '
                    . 'LEFT JOIN exchange_ticket_codes etc ON kt.id = etc.ticket_id AND etc.esp_type = "ticket" '
                    . 'LEFT JOIN exchange_ticket_codes_state kz ON kz.exchange_ticket_codes_id = etc.id '
                    . 'WHERE kt.status > 0 '
                    . 'AND kt.id_usera IN (' . $users . ')';

                $this->sdb->query($sql2);
                while ($res = $this->sdb->fetch_row()) {
                    $karty[$res['serial_karty']]['liczba_dni'] = $res['liczba_dni'];
                    $karty[$res['serial_karty']]['numer_zamowienia'] = $res['id_zamowienia'];
                    $karty[$res['serial_karty']]['ciagly'] = $res['ciagly'];
                    $karty[$res['serial_karty']]['status'] = $res['status'];
                    $karty[$res['serial_karty']]['ile'] = $res['ile'];
                }

                $this->tpl_data = array('karty' => $karty);
                if ($_GET['s']) {
                    $this->tpl_data['dtfilter'] = $_GET['s'];
                }
                $this->Display('karty', 'Lista kart partnera');
                break;

        }

    }

    public function KartyOsrodek($vars, $strcount = 10, $s = 1)
    {
        if (isset($vars['iDisplayStart']) && $vars['iDisplayLength'] != '-1') {
            $s = (int)floor((int)$vars['iDisplayStart'] / (int)$vars['iDisplayLength']) + 1;
            $strcount = (int)$vars['iDisplayLength'];
        }
        if ($s === 0) {
            $s = 1;
        }
        if ($strcount === 0) {
            $strcount = 10;
        }
        //key -> numer kolumny value -> pole z uwzględnieniem aliasu
        $columny = array(
            0 => 'v.id',
            1 => 'v.serial_karty',
            4 => 'v.id_zamowienia',
            5 => 'u.email',
            6 => 'v.ts',
            11 => 'v.status'
        );

        $sql = 'SELECT SQL_CALC_FOUND_ROWS v.*, u.email, z.id_zamowienia_mgo, zp.zwrot, zp.offer_id, pl.partner_gate_used, etc.status as etc_status FROM ' . TABLE_PREFIX . 'karnety v '
            . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia z on v.id_zamowienia = z.id '
            . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia_przychod zp ON zp.id_zamowienia = z.id AND zp.parent_id = v.id AND zp.typ_produktu = "karnety" '
            . 'LEFT JOIN ' . TABLE_PREFIX . 'platnosci pl ON pl.item = z.id '
            . 'LEFT JOIN exchange_tid et ON v.tid = et.tid '
            . 'LEFT JOIN exchange_ticket_codes etc ON et.id = etc.tid_id '
            . 'LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik u ON u.id = z.id_usera '
            . 'WHERE z.data_platnosci > 0 AND zp.offer_id in (SELECT id_oferty from ' . TABLE_PREFIX . 'partners_offers WHERE id_partnera = ' . $this->partner['partner']['id'] . ') '
            . 'AND v.status NOT IN (-100)';

        if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
            $ss = $this->sdb->ESCAPE($vars['sSearch']);
            $sql .= ' AND (v.serial_karty LIKE "%' . $ss . '%" OR '
                . 'v.id_zamowienia LIKE "%' . $ss . '%" OR '
                . 'u.email LIKE "%' . $ss . '%")';
        }

        if (isset($vars['iSortCol_0'])) {
            for ($i = 0; $i < (int)$vars['iSortingCols']; $i++) {
                if ($vars['bSortable_' . (int)$vars['iSortCol_' . $i]] === "true") {
                    $sql .= ' ORDER BY ' . $columny[(int)$vars['iSortCol_' . $i]] . ' ' . ($vars['sSortDir_' . $i] === 'asc' ? 'ASC' : 'DESC');

                }
            }
        }

        $sql .= ' LIMIT ' . ($s - 1) * $strcount . ', ' . $strcount;
        $dane = $this->sdb->select($sql);
        $sqlog = [];
        $sqlog[] = $sql;
        if (false === is_array($dane)) {
            $dane = array();
        }
        $fullrows = $this->sdb->select_r('SELECT FOUND_ROWS() as ile');
        $totalall = $fullrows['ile'];
        $output = array(
            "sEcho" => (int)$vars['sEcho'],
            "iTotalRecords" => $totalall,
            "iTotalDisplayRecords" => $totalall,
            "aaData" => array()
        );
        $mgo = Mymongo::activate();
        $usesrs = [];
        foreach ($dane as $v) {
            if (empty($users[$v['id_usera']])) {
                $users[$v['id_usera']] = User::getUserAccountAddress($v['id_usera']);
            }
            if (!empty($users[$v['id_usera']]['osobiste'])) {
                $no = count($users[$v['id_usera']]['osobiste']);
                $v['kod_pocztowy'] = $users[$v['id_usera']]['osobiste'][$no - 1]['kod'];
            }
            $nazwaKarnetu = null;
            $liczba_dni = null;
            $liczba_godzin = null;
            $nazwaWariantu = null;
            $getOffer = $mgo->mget_offer_details($v['offer_id']);
            if (!empty($getOffer['karnety'])) {
                foreach ($getOffer['karnety'] as $offer) {
                    if ($offer['identyfikator'] == $v['id_produktu_oferta']) {
                        $nazwaKarnetu = $offer['nazwa'][LANG];
                        $liczba_dni = $offer['liczba_dni'];
                        $liczba_godzin = $offer['liczba_godzin'];
                        foreach ($offer['wariant_cenowy'] as $wariant) {
                            if ($wariant['bsid'] == $v['bsid']) {
                                $nazwaWariantu = $wariant['nazwa'];
                            }
                        }
                        $v['karnet'] = ['nazwa' => $nazwaKarnetu, 'wariant' => $nazwaWariantu, 'dni' => $liczba_dni, 'godziny' => $liczba_godzin];
                    }
                }
                $sql2 = "SELECT exc.dni, ext.nr 
                    FROM exchange_ticket_codes_state exc 
                    LEFT JOIN exchange_ticket_codes ext 
                    ON exc.exchange_ticket_codes_id = ext.id 
                    WHERE ext.order_id = " . $v['id_zamowienia'] .
                    " AND ext.esp_type = 'ticket'";
                $karta = $this->sdb->select($sql2);
                $sqlog[] = $sql2;
                if (!empty($karta)) {
                    $v['karta'] = $karta;
                }
            }

            $button = '';
            if ((int)$v['tid'] !== 0 && (int)$v['status'] === 1 && (int)$v['etc_status'] === 1 && (int)$v['partner_gate_used'] === 1) {
                $button = '<td></td><button class="btn btn-outline btn-danger" data-id="' . $v['id'] . '"><i class="glyphicon glyphicon-off"></i></button></td>';
            } elseif ((int)$v['tid'] !== 0 && (int)$v['status'] === 1 && (int)$v['etc_status'] === 2 && (int)$v['partner_gate_used'] === 1) {
                $button = '<td></td><button class="btn btn-outline btn-danger" data-id="' . $v['id'] . '" disabled><i class="glyphicon glyphicon-off"></i></button></td>';
            }
            $nosnik = 'karta';
            if ($v['nosnik'] === 'voucher') {
                $link = Voucher::getPdfLinkKartaVoucher($v['id_karty'], 'partner');
                $nosnik = '<a href="' . $link . '" target="_blank"> PDF </a>';
            }
            $output['aaData'][] = array(
                //id
                $v['id'],
                //numer_karty
                $v['karta']['nr'] ?? $v['serial_karty'],
                //karnet - nazwa
                $v['karnet']['nazwa'] ?? '-',
                $nosnik,
                //nr zamowienia
                $v['id_zamowienia'] ?? '-',
                //email
                $v['email'] ?? '-',
                empty($v['kod_pocztowy']) ? '--' : $v['kod_pocztowy'],
                //data zamowienia
                implode('<br>', explode(' ', $v['ts'])),
                //ilosc dni karnetu
                $v['karnet']['dni'] ?? '-',
                //ilosc godzin karnetu
                $v['karnet']['godziny'] ?? '-',
                //ilosc pozostalych
                $v['karta']['dni'] ?? $v['karnet']['dni'] ?? '-',
                //wariant karnetu
                $v['karnet']['wariant'] ?? '-',
                //status
                $this->app->Lang($this->statusy[$v['status']], "Partner"),
                $button
            );
        }

//        $output['sqllog'] = $sqlog;
        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode($output));
    }

    /**
     * @todo dorobić sprawdzanie stanu vouchera i listę voucherów
     */
    public function StanVouchera()
    {
        if ($_GET['s']) {
            $this->tpl_data['dtfilter'] = $_GET['s'];
        }
        $this->Display('vouchery', 'Lista voucherów');
    }

    public function AjaxListVouchers($vars, $strcount = 10, $s = 1)
    {

        if (isset($vars['iDisplayStart']) && (int)$vars['iDisplayLength'] !== -1) {
            $s = (int)floor((int)$vars['iDisplayStart'] / (int)$vars['iDisplayLength']) + 1;
            $strcount = (int)$vars['iDisplayLength'];
        }
        if ($s === 0) {
            $s = 1;
        }
        if ($strcount === 0) {
            $strcount = 10;
        }


        $columny = array(
            'v.id',
            'z.id_zamowienia_mgo',
            'v.serial_karty',
            'u.email',
            'v.status',
        );

        if ($this->partner['partner']['partnertype'] == 2) {
            $sql = 'SELECT SQL_CALC_FOUND_ROWS v.*, u.email, z.id_zamowienia_mgo, zp.zwrot , pl.partner_gate_used, etc.status as etc_status FROM ' . TABLE_PREFIX . 'vouchery v '
                . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia z on v.id_zamowienia = z.id '
                . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia_przychod zp ON zp.id_zamowienia = z.id AND zp.parent_id = v.id AND zp.typ_produktu = "vouchery" '
                . 'LEFT JOIN ' . TABLE_PREFIX . 'platnosci pl ON pl.item = z.id '
                . 'LEFT JOIN exchange_tid et ON et.tid = v.tid '
                . 'LEFT JOIN exchange_ticket_codes etc ON et.id = etc.tid_id '
                . 'LEFT JOIN ' . TABLE_PREFIX . 'uzytkownik u ON u.id = z.id_usera '
                . 'WHERE z.data_platnosci > 0 AND zp.offer_id in (SELECT id_oferty from ' . TABLE_PREFIX . 'partners_offers WHERE id_partnera = ' . $this->partner['partner']['id'] . ') ';
        } else {
            if ($this->partner['partner']['partnertype'] == 1) {
                $sql = 'SELECT SQL_CALC_FOUND_ROWS v.*, z.id_zamowienia_mgo, zp.zwrot, pl.partner_gate_used FROM ' . TABLE_PREFIX . 'vouchery v '
                    . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia z on v.id_zamowienia = z.id '
                    . 'LEFT JOIN ' . TABLE_PREFIX . 'zamowienia_przychod zp ON zp.id_zamowienia = z.id AND zp.parent_id = v.id AND zp.typ_produktu = "vouchery" '
                    . 'LEFT JOIN ' . TABLE_PREFIX . 'platnosci pl ON pl.item = z.id '
                    . 'WHERE  z.data_platnosci > 0 AND v.installation = ' . INSTALLATION;
            }
        }
        if (isset($vars['sSearch']) && $vars['sSearch'] != "") {
            $ss = $this->sdb->ESCAPE($vars['sSearch']);
            $sql .= ' AND (v.serial LIKE "%' . $ss . '%" OR '
                . 'v.serial_karty LIKE "%' . $ss . '%" OR '
                . 'u.email LIKE "%' . $ss . '%" OR z.id LIKE "%' . $ss . '%")';
        }

        if (isset($vars['iSortCol_0'])) {
            for ($i = 0; $i < (int)$vars['iSortingCols']; $i++) {
                if ($vars['bSortable_' . (int)$vars['iSortCol_' . $i]] === "true") {
                    $sql .= ' ORDER BY ' . $columny[(int)$vars['iSortCol_' . $i]] . ' ' . ($vars['sSortDir_' . $i] === 'asc' ? 'ASC' : 'DESC');
                }
            }
        }

        $sql .= ' LIMIT ' . ($s - 1) * $strcount . ', ' . $strcount;
        $dane = $this->sdb->select($sql);
        if (false === is_array($dane)) {
            $dane = array();
        }
        $fullrows = $this->sdb->select_r('SELECT FOUND_ROWS() as ile');
        $totalall = $fullrows['ile'];
        $output = array(
            "sEcho" => (int)$vars['sEcho'],
            "iTotalRecords" => $totalall,
            "iTotalDisplayRecords" => $totalall,
            "aaData" => array()
        );

        $users = [];
        foreach ($dane as &$v) {
            $pr = OrderData::FindOrderProduct($v['id_zamowienia_mgo'], 'voucher', $v['id_produktu_zamowienie']);
            if (empty($users[$v['id_usera']])) {
                $users[$v['id_usera']] = User::getUserAccountAddress($v['id_usera']);
            }
            $v['nazwa'] = $pr['nazwa'];
            $v['wariant'] = $pr['cena']['cena_nazwa'];
            if (!empty($users[$v['id_usera']]['osobiste'])) {
                $no = count($users[$v['id_usera']]['osobiste']);
                $v['kod_pocztowy'] = $users[$v['id_usera']]['osobiste'][$no - 1]['kod'];
            }
//            $pg = OrderData::FindOrderProduct($v['id_zamowienia_mgo'], 'produkt_grupowy', $v['id_produktu_zamowienie']);
            if ($v['zwrot'] > 0) {
                $status = 'zwrócony';
            } else {

                $status = $this->app->Lang($this->statusy[$v['status']], "Partner");
            }
            $pdf_link = $this->app->Lang('Brak');
            if ($v['nosnik'] === 'voucher') {
                $link = Voucher::getPdfLinkKartaVoucher($v['id_karty'], 'partner');
                $nosnik = '<a href="' . $link . '" target="_blank"> PDF </a>';
            }
            if (!empty($v['pdf'])) {
                $pdf_link = '<a href="' . App::GenerateLink('vouchery', $v['id'], false, 'partner') . '" target ="_blank">' . App::_Lang('Pobierz') . '</a>';
            }

            $button = '';
            if ((int)$v['tid'] !== 0 && (int)$v['status'] === 1 && (int)$v['etc_status'] === 1 && (int)$v['partner_gate_used'] === 1) {
                $button = '<td></td><button class="btn btn-outline btn-danger" data-id="' . $v['id'] . '"><i class="glyphicon glyphicon-off"></i></button></td>';
            } elseif ((int)$v['tid'] !== 0 && (int)$v['status'] === 1 && (int)$v['etc_status'] === 2 && (int)$v['partner_gate_used'] === 1) {
                $button = '<td></td><button class="btn btn-outline btn-danger" data-id="' . $v['id'] . '" disabled><i class="glyphicon glyphicon-off"></i></button></td>';
            }

            $output['aaData'][] = array(
                $v['id'],
                $v['id_zamowienia'],
                $v['email'],
                empty($v['kod_pocztowy']) ? '--' : $v['kod_pocztowy'],
                $v['serial_karty'],
                $v['nazwa'],
                $v['wariant'],
                $pdf_link,
                $status,
                $button
            );
        }

        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode($output));
    }

    public function Voucherinfo()
    {
        require 'application/website_v4_0/modules/partner/Details.php';
        $det = new Details($this->app);
        $det->Voucherinfo();
    }

    public function ReturnVoucher()
    {
        require 'application/website_v4_0/modules/partner/Details.php';
        $return = new Details($this->app);
        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode(["success" => $return->ReturnVoucher($_POST['ptype'])]));
    }

    public function GetCardStatus()
    {
        require 'application/website_v4_0/modules/partner/Details.php';
        $det = new Details($this->app);
        $det->AjaxGetCardStatus();
    }

    public function GetCardHistory()
    {
        require 'application/website_v4_0/modules/partner/Details.php';
        $det = new Details($this->app);
        $det->AjaxGetHistory();
    }


    protected function _getProductsGroups($idoferty = false)
    {
        $sql_r = 'SELECT gpl.parentid as id, gpl.value as nazwa FROM ' . TABLE_PREFIX . 'grupy_produktowe_langs gpl LEFT JOIN ' . TABLE_PREFIX . 'grupy_produktowe gp ON gp.id = gpl.parentid WHERE gpl.property = "nazwa" AND gpl.langid=' . LANG;
        if ($idoferty) {
            $sql_r .= ' AND gp.id_oferty="' . $idoferty . '"';
        }
        $res = $this->sdb->select($sql_r);
        foreach ($res as $v) {
            $ret[$v['id']] = $v;
        }
        return $ret;
    }


}

