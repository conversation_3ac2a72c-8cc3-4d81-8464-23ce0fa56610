<?php
include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');

/**
 * @@@My account@@@
 * Metody do uprawnień: 
 * @package Modules
 * @subpackage Account
 *
 */
class Account extends Module
{
    public $app                   = null;
    public $get                   = array();
    public $post                  = array();
    public $alerts                = array();
    protected $sdb                = null;
    protected $mdb                = null;
    protected $conf_tpl           = array();
    protected $ajax_data          = array();
    protected $ajax_resp          = array();
    protected $widget_header_data = array();
    protected $widget_accnav_data = array();
    protected $tab                = null;
    protected $tabs               = array( 'profile', 'product', 'settings', 'data', 'orders', 'cards', 'tickets', 'vouchers', 'coupons', 'stamps', 'insurances', 
        'mediations', 'returns', 'bon' 
        );
    protected $return_types       = array( 'withdrawal' => 1, 'return' => 2, 'reclamation' => 3 );
    public $meta                 = null; // Offer metadata
    public $mgo                 = null; // Offer metadata


    public function __construct( $app )
    {
        $this->app  = &$app;
        $this->sdb  = Database::activate();
        $this->get  = $this->app->urlparam;
        $this->post = $this->app->postparam;
        $this->mgo = Mymongo::activate();
        $this->getOfferMeta();

        if ( Sessions::IsLoggedIn() )
        {
            Router::activate( $this );
        }
        switch ( $this->get['a'] )
        {
            default                   : $this->_r('main');               break;
            case 'getpdf'             : $this->_r('getPDF');               break;
            case 'getpdfpackage'             : $this->_r('getPdfPackage',  $_POST);               break;
            case 'orderinvoice'       : $this->orderinvoice();       break;
//            case 'orderinvoice'       : $this->_r('orderinvoice');       break;
            case 'ajaxchangedata'     : $this->_r('ajaxchangedata');     break;
            case 'ajaxchangeaddress'  : $this->_r('ajaxchangeaddress');  break;
            case 'ajaxaddaddress'     : $this->_r('ajaxaddaddress');     break;
            case 'ajaxchangecarddesc' : $this->_r('ajaxchangecarddesc'); break;
            case 'ajaxcancelorder'    : $this->_r('ajaxcancelorder');    break;
            case 'ajaxauth'           : $this->_r('authajax');           break;
            case 'ajaxvalidateinvoice': $this->_r('ajaxvalidateinvoice');break;
            case 'newmediation'       : $this->_r('newmediation');       break;
            case 'mediation'          : $this->mediation();              break;
            case 'workoffer'          : $this->workOffer();              break;
            case 'dlfile'             : $this->DownloadFile();           break;
            case 'test'               : $this->test();                   break;
        }
    }

    public function getPdfPackage($param){
        ini_set('max_execution_time', 120);
        $id_zamowienia = $param['order_id'];
        $id_usera = User::GetUserID();
        $response = Voucher::GetDownloadPDF($id_zamowienia, 'user', $id_usera);
        $this->app->SetDecorator('Ajax');
        $this->app->ADD('response', json_encode(['link' => $response['link'], 'status' => $response['status'], 'err' => $response['err']]));
    }
    public function getOfferMeta()
    {
        $where = array(
            'status.id'       => 5,
            'current'         => 1,
            'partnerzy_sp.id' => INSTALLATION,
        );

        $cursor = $this->mgo->db->offers->findOne($where);
        $currentoffer = $this->mgo->Mgo2array($cursor);
        $this->lang = $currentoffer['lang'] ?: $_SESSION['lang'];
        if(!defined('LANG')){
            define('LANG', $this->lang);
        }
        $this->currency = $currentoffer['currency'] ?: $_SESSION['CURRENCY'];
//        die(var_dump($this->lang, $this->currency));
        $this->app->SetCurrency($this->currency, true);
        $this->meta = $currentoffer['meta'];
        $this->widget_header_data['offerid'] = $currentoffer['parentid'];
    }

    public function test()
    {

    }

    public function main()
    {
        if ( in_array( $this->get['tab'], $this->tabs ) )
        {
            $this->tab = $this->get['tab'];
        }
        else
        {
            $this->tab = 'profile';
        }

        call_user_func( array ( $this, 'tab' . $this->tab ) );
        $this->tpl_conf['tpl'] = $this->tab;
        $this->display( 'main' );
    }
    

    public function ajaxAddAddress()
    {
        $dane = $this->post['data'];
        $type = $this->post['type'];

        $val = Validation::basicAddressValidation( $dane, true );

        if ( $val['errors'] )
        {
            $resp['error'] = $val['warnings'];
        }
        else
        {
            switch ( $type )
            {
                case 'invoice':
                    $dane['rodzaj'] = 'faktura';
                    break;
                
                case 'transport':
                    $where = array();
                    $where[] = array( 'rodzaj', 'osobiste', 'STRING' );
                    $where[] = array( 'item', User::GetUserID(), 'INT' );
                    
                    if( ! $this->sdb->get_filtered_rows( 'uzytkownik_meta', 'id', $where ) )
                    {
                        $dane['rodzaj'] = 'osobiste';
                    }
                    else
                    {
                        $dane['rodzaj'] = 'adres';    
                    }
                    break;

                case 'correspondence':
                    $where = array();
                    $where[] = array( 'rodzaj', 'osobiste', 'STRING' );
                    $where[] = array( 'item', User::GetUserID(), 'INT' );
                    $id = $this->sdb->get_filtered_rows( 'uzytkownik_meta', 'id', $where );
                    
                    if( !$id )
                    {
                        $update = false;
                        $dane['rodzaj'] = 'osobiste';
                    }
                    else
                    {
                        $update = true;   
                    }
                    break;
            }
            
            if ( !$update )
            {
                $data[] = array( 'imie',     $dane['imie'],     'STRING' );
                $data[] = array( 'nazwisko', $dane['nazwisko'], 'STRING' );
                $data[] = array( 'adres',    $dane['adres'],    'STRING' );
                $data[] = array( 'miasto',   $dane['miasto'],   'STRING' );
                $data[] = array( 'kod',      $dane['kod'],      'STRING' );
                $data[] = array( 'kraj',     $dane['kraj'],     'STRING' );
                $data[] = array( 'tel',      $dane['tel'],      'STRING' );
                $data[] = array( 'email',    $dane['email'],    'STRING' );
                $data[] = array( 'rodzaj',   $dane['rodzaj'],   'STRING' );
                $data[] = array( 'item',     USER::GetUserID(), 'INT' );
                
                if ( $dane['nip'])
                {
                    $data[] = array( 'nip',      $dane['nip'],      'STRING' );
                }


                if ($dane['firma'] )
                {
                    $data[] = array( 'firma',    $dane['firma'],    'STRING' );
                }

                
                if ( ! $this->sdb->insert_row( 'uzytkownik_meta', $data ) ) 
                {
                    $resp['error'] = $this->app->Lang('Nie udało się dodać adresu do bazy','F_Konto użytkownika');
                }
                else
                {
                    $resp['dane'] = $dane;
                    $resp['dane']['id'] = $this->sdb->last_id();
                }
            }
            else
            {
                $sql = 'UPDATE ' . TABLE_PREFIX . 'uzytkownik_meta SET ';
                $sql.= 'imie ="'     . $this->sdb->escape($dane['imie'])     . '", ';
                $sql.= 'nazwisko ="' . $this->sdb->escape($dane['nazwisko']) . '", ';
                $sql.= 'adres ="'    . $this->sdb->escape($dane['adres'])    . '", ';
                $sql.= 'miasto ="'   . $this->sdb->escape($dane['miasto'])   . '", ';
                $sql.= 'kod ="'      . $this->sdb->escape($dane['kod'])      . '", ';
                $sql.= 'kraj ="'     . $this->sdb->escape($dane['kraj'])     . '", ';
                $sql.= 'tel ="'      . $this->sdb->escape($dane['tel'])      . '", ';
                $sql.= 'email ="'    . $this->sdb->escape($dane['email'])    . '" ';
                $sql.= 'WHERE item = ' . User::GetUserID() . ' ';
                $sql.= 'AND rodzaj = "osobiste" ';

                if ( ! $this->sdb->query( $sql ) ) 
                {
                    $resp['error'] = $this->app->Lang('Nie udało się zaktualizować adresu w bazie','F_Konto użytkownika');
                }
                else
                {
                    $resp['dane'] = $dane;
                    $resp['dane']['id'] = $id;
                }
            }
        }

        $this->app->ADD( 'response', json_encode( $resp ) );
        $this->app->SetDecorator( 'Ajax' );
    }


    protected function DownloadFile()
    {
        $post = $this->app->postparam;

        if ( $post['id'] )
        {
            $mongo = Mymongo::activate();
            $mongo->download_file( $post['id'] );
        }
    }


    protected function newMediation()
    {
        if($this->post)
        {
            $val[] = array( $this->post['tytul_sprawy'], 'REQUIRED', $this->app->Lang('Tytuł','F_Konto użytkownika'));
            $val[] = array(array($this->post['tytul_sprawy'], 64),  'MAX_LENGTH', $this->app->Lang('Tytuł','F_Konto użytkownika'));
            $val[] = array(array($this->post['tytul_sprawy'], 2 ),  'MIN_LENGTH', $this->app->Lang('Tytuł','F_Konto użytkownika'));
            $val[] = array( $this->post['email_klienta'], 'REQUIRED', $this->app->Lang('Twój email','F_Konto użytkownika'));
            $val[] = array( $this->post['email_klienta'], 'EMAIL',    $this->app->Lang('Twój email','F_Konto użytkownika'));
            $val[] = array( $this->post['kategoria_sprawy'], 'REQUIRED', $this->app->Lang('Kategoria sprawy','F_Konto użytkownika'));
            $val = Validation::ValForm($val);

            if($val['errors'])
            {
                $this->alerts['danger'] = $val['warnings'];
            }
            else
            {
                $this->post['operacja']  = 'add';
                $this->post['user_type'] = 'client';
                $case = Mediation::UpdateCase($this->post);
                header('Location: ' . SITE_URL . 'account/mediation?id_ticket=' . $case['id_ticket'] . '&hash=' . $case['hash']);
            }
        }

        $this->tpl_data['postback']  = $this->post;
        $this->tpl_data['useremail'] = User::GetUserEmail();
        $this->tpl_data['kategorie'] = Additional::MediationCategories();
        $this->display('new-mediation','Nowa mediacja');
    }
    

    protected function mediation()
    {
        $sprawa = null;
        if ( $this->get['id_ticket'] && $this->get['hash'] )
        {
            $this->mdb = Mymongo::activate();
            $sprawa = $this->mdb->case_list( 'all', false, $this->get['id_ticket'], true, false, $this->get['hash'] );

            if ( $sprawa )
            {
                if ( $sprawa['hash'] == $this->get['hash'] && $sprawa['id_ticket'] == $this->get['id_ticket'] )
                {
                    if ( $this->post['id_ticket'] )
                    {
                        if ( $this->MediaValidate() )
                        {
                            $this->post['user_type'] = 'client';
                            
                            if ( Mediation::SendEmail( $this->post, $sprawa, false ) )
                            {
                                foreach ( $sprawa['prowadzacy'] as $prowadzacy )
                                {
                                    $rec = $prowadzacy;
                                    $module = 'mediation';
                                    $item = 'admin.php?mediation&a=show&id_ticket=' . $this->post['id_ticket'];
                                    $mes = 'Nowa wiadomość w mediacji';
                                    Sms::Send( $rec, $module, $item, $mes );
                                }
                                
                                $sprawa = $this->mdb->case_list( 1, false, $this->get['id_ticket'], true );
                                $this->alerts['success'][] = $this->app->Lang('Wysłano odpowiedź','F_Konto użytkownika');
                            }
                            else
                            {
                                $this->alerts['danger'][] = $this->app->Lang('Nie udało się wysłać wiadomości!','F_Konto użytkownika');
                            }
                        }
                    }
                }
                else
                {
                    $this->alerts['danger'][] = $this->app->Lang('Nieprawidłowy kod sprawy','F_Konto użytkownika');
                    $sprawa = array();
                }
            }
            else
            {
                $this->alerts['danger'][] = $this->app->Lang('Sprawa nie istnieje lub nie jest już aktywna','F_Konto użytkownika');
            }
        }
        
        $this->tpl_data['userlogged'] = Sessions::IsLoggedIn();
        $this->tpl_data['sprawa'] = $sprawa;
        $this->widget_header_data['title'] = $this->app->Lang('Mediacja','F_Konto użytkownika');
        $this->display('mediation-client');
    }
    

    private function MediaValidate()
    {
        $val[] = array ( $this->post['email_klienta'],               'REQUIRED',   $this->app->Lang('Twój email','F_Konto użytkownika'));
        $val[] = array ( $this->post['temat'],                       'REQUIRED',   $this->app->Lang('Temat wiadomości','F_Konto użytkownika'));
        $val[] = array ( $this->post['tresc'],                       'REQUIRED',   $this->app->Lang('Treść wiadomości','F_Konto użytkownika'));
        $val[] = array ( $this->post['email_klienta'],               'EMAIL',      $this->app->Lang('Twój email','F_Konto użytkownika'));
        $val[] = array ( array ( $this->post['email_klienta'], 70 ), 'MAX_LENGTH', $this->app->Lang('Twój email','F_Konto użytkownika'));
        $val[] = array ( array ( $this->post['temat'], 100 ),        'MAX_LENGTH', $this->app->Lang('Temat wiadomości','F_Konto użytkownika'));
        $val[] = array ( array ( $this->post['tresc'], 10000 ),      'MAX_LENGTH', $this->app->Lang('Treść wiadomości','F_Konto użytkownika'));
        $val[] = array ( array ( $this->post['temat'], 2 ),          'MIN_LENGTH', $this->app->Lang('Temat wiadomości','F_Konto użytkownika'));
        $val[] = array ( array ( $this->post['tresc'], 10 ),         'MIN_LENGTH', $this->app->Lang('Treść wiadomości','F_Konto użytkownika'));
        $val = Validation::ValForm( $val );
        
        if ( !$val['errors'] )
        {
            return true;
        }
        else
        {
            $this->alerts['warning'] = $val['warnings'];
            return false;
        }
    }
    

    private function tabProfile()
    {
        $products = array ( 'zamowienia' );
        $settings = array (
            'add_card_tickets'     => true,
            'add_ticket_days_left' => true,
            'set_resorts_string'   => true,
            'add_paid_products'    => true,
        );
        $up = new UserProductsData();
        $up->setFormatSettings( $settings );
        $up->getProductsCount();
        $up->setOutputProducts( $products );
        $up->setOutputProductsLimit( 5 );
        $up->getProducts();
        $this->tpl_data = $up->rOutput();
        $sql = 'SELECT count(id) as oc FROM ' . TABLE_PREFIX . 'zamowienia ';
        $sql.= 'WHERE id_usera = ' . User::GetUserID() . ' ';
        $sql.= 'AND status = -1 ';
        $res = $this->sdb->select_r($sql);
        $this->tpl_data['zamowienia_nieoplacone'] = $res['oc'];

        $links = array(
            'zamowienia' => 'orders',
            'karty'      => 'cards',
            'karnety'    => 'tickets',
            'vouchery'   => 'vouchers',
            'kupony'     => 'coupons',
            'pieczatki'  => 'stamps',
        );

        foreach ( $this->tpl_data['p_quantity'] as $key => &$p )
        {
            $p['link'] = $links[ $key ];
        }

        $this->tpl_data['bon'] = Bon::getBonSum(true, false);
        $this->tpl_data['userdata']['nazwa'] = USER::GetUserFullName();
        $this->widget_accnav_data['profile'] = $this->tpl_data;
    }



    private function tabSettings()
    {
        $this->tabProfile();
        $this->tpl_data['userdata']['imie']     = USER::GetUserFName();
        $this->tpl_data['userdata']['nazwisko'] = USER::GetUserSName();
        $this->tpl_data['userdata']['email']    = USER::GetUserEmail();
    }

    private function tabData()
    {
        $this->tabProfile();
        $this->tpl_data = USER::getUserAccountAddress();
        $this->tpl_data['countries'] = Additional::getCountriesList( LANG ); 
    }
    
    private function tabReturns()
    {

        $this->tabProfile();
        if ($this->app->settings['partner_wlasne_zwroty'] == 1) {
            return;
        }
        $types = array( 'return', 'withdrawal', 'reclamation', 'mediations' );
        
        if ( in_array( $this->get['type'], $types ) )
        {
            $type = $this->get['type'];
        }
        else
        {
            $type = 'withdrawal';
        }
        
        switch ( $type )
        {
            // Zwrot gwarantowany

            case 'return':
                if ( $this->post )
                {
                    try 
                    {
                        $this->makeReturn( $type );
                        $this->alerts['success'][] = $this->app->Lang('Dodano zgłoszenie','F_Konto użytkownika');
                    }
                    catch ( Exception $e )
                    {
                        if ( $e->getMessage() )
                        {
                           $this->alerts['danger'][] = $e->getMessage();
                        }
                    }
                }

                $this->tpl_data['zamowienia'] = ReturnData::ReturnData();
                break;
            

            // Odstąpienie od umowy

            case 'withdrawal':
                if ( $this->post )
                {
                    try 
                    {
                        $this->makeReturn( $type );
                        $this->alerts['success'][] = $this->app->Lang('Dodano zgłoszenie','F_Konto użytkownika');
                    }
                    catch ( Exception $e )
                    {
                        if ( $e->getMessage() )
                        {
                           $this->alerts['danger'][] = $e->getMessage();
                        }
                    }
                }

                $this->tpl_data['zamowienia'] = ReturnData::WithdrawalData();
                break;
            

            // Reklamacja

            case 'reclamation':
                if ( $this->post )
                {
                    $this->makeReturn( $type );
                    $this->alerts['success'][] = $this->app->Lang('Dodano zgłoszenie','F_Konto użytkownika');
                }
                $this->tpl_data['zamowienia'] = ReturnData::ReclamationData();
                break;
                

            // Lista mediacji

            case 'mediations':
                $this->mdb = Mymongo::activate();
                $operators = array();
                $operators[]['$match'] = array(
                    'kategoria_sprawy' => 'returns',
                    'id_klienta' => (string) User::GetUserID(),
                );
                $operators[]['$sort'] = array('id_ticket' => -1);
                $operators[]['$project'] = array(
                    '_id'            => 0,
                    'hash'           => 1,
                    'id_ticket'      => 1,
                    'data_zalozenia' => 1,
                    'tytul_sprawy'   => 1,
                    'status_sprawy'  => 1,
                    'id_zamowienia'  => 1,
                    'rodzaj_sprawy'  => 1,
                    'prowadzacy'     => 1,
                );
                $this->tpl_data['mediations'] = $this->mdb->Aggregate( $operators, 'case' );
                break;
        }
        $this->app->AddDecoratorData( 'return_type', $type );
    }


    private function makeReturn( $type )
    {
        // Sprawdzanie danych
        // Tools::PA($this->post); die();
        
        ReturnData::returnValidation( $this->post );
        $order = ReturnData::getOrder( $this->post );
        $data  = ReturnData::setOrderData( $type, $this->post, $order );
        $order = $data['zamowienie'];
        $post  = $data['post'];

        // Zapisywanie danych

        $post  = ReturnData::saveReturn( $post, $order, false, 'client' );
        $case  = Mediation::UpdateCase( $post );
        $post['id_ticket'] = $case['id_ticket'];
        ReturnData::SaveReturnData( $post );

        // Wysyłanie powiadomienia
        
        if ( $type != 'reclamation' )
        {
            $order_details = ReturnData::getOrderDetails($order, $post['rodzaj_zwrotu']);
            $this->app->AddDecoratorData( 'data', $order_details );
            $this->app->SetTemplate('modules/account/mail/return-order.tpl');
            $temp = new DecoratorSmarty( $this->app );
            $order_data_html = $temp->output;   
        }
        else
        {
            $order_data_html = false;
        }

        ReturnData::sendReturnNotification( $order_data_html, $case, $type );
    }
    
    
    private function tabMediations()
    {
        $this->tabProfile();
        $this->mdb = Mymongo::activate();
        
        $operators = array();
        $operators[]['$match'] = array(
            'kategoria_sprawy' => 'offers',
            'id_klienta' => (string) User::GetUserID(),
        );
        $operators[]['$project'] = array(
            '_id'            => 0,
            'hash'           => 1,
            'id_ticket'      => 1,
            'data_zalozenia' => 1,
            'tytul_sprawy'   => 1,
            'status_sprawy'  => 1,
            'prowadzacy'     => 1,
        );
        $this->tpl_data['offers'] = $this->mdb->Aggregate( $operators, 'case' );

        $operators = array();
        $operators[]['$match'] = array(
            'kategoria_sprawy' => 'others',
            'id_klienta' => (string) User::GetUserID(),
        );
        $operators[]['$project'] = array(
            '_id'            => 0,
            'hash'           => 1,
            'id_ticket'      => 1,
            'data_zalozenia' => 1,
            'tytul_sprawy'   => 1,
            'status_sprawy'  => 1,
            'prowadzacy'     => 1,
        );
        $this->tpl_data['others'] = $this->mdb->Aggregate( $operators, 'case' );
    }
    
    private function tabOrders()
    {
        $this->tabProfile();
        $settings = array (
            'add_active'          => true,
            'add_partners'        => true,
            'set_partners_string' => true,
            'del_order_suborders' => true,
        );
        $products = array ( 'zamowienia' );
        $up = new UserProductsData();
        $up->setFormatSettings( $settings );
        $up->setOutputProducts( $products );
        $up->getProducts();
        $this->tpl_data = $up->rOutput();
    }
    
    private function tabCards()
    {
        $this->tabProfile();
        $settings = array ( 
            'add_card_active_tickets_num' => true,
            'add_card_change_img'         => true,
            'add_paid_products'           => true,
        );
        $products = array ( 'karty' );
        $up = new UserProductsData();
        $up->setOutputProducts( $products );
        $up->setFormatSettings( $settings );
        $up->getProducts();
        $this->tpl_data = $up->rOutput();
    }
    
    private function tabTickets()
    {
        $this->tabProfile();
        $settings = array ( 
            'set_resorts_string'   => true,
            'add_ticket_days_left' => true,
            'add_paid_products'    => true,
        );
        $products = array ( 'karnety', 'vouchery' );
        $up = new UserProductsData();
        $up->setOutputProducts( $products );
        $up->setFormatSettings( $settings );
        $up->getProducts();
        $up->output['products'] = $up->mergeProducts();
        $this->tpl_data = $up->rOutput();
    }

    public function getPDF()
    {
        if(!empty($user = User::GetUserID()))
        {
            if($this->get['type'] == 'k' ) {
                $file = $this->sdb->select('SELECT pdf,serial FROM '.TABLE_PREFIX.'karty_voucher WHERE serial='.$this->get['ticket']. ' LIMIT 1');
                $_id = $this->sdb->select('SELECT id_usera FROM '.TABLE_PREFIX.'karnety WHERE serial_karty='.$this->get['ticket']. ' LIMIT 1');
                $user_id = $_id[0]['id_usera'];
            } else {
                $file = $this->sdb->select('SELECT id_usera,pdf FROM '. TABLE_PREFIX.'vouchery WHERE id='.$this->get['ticket']. ' LIMIT 1');
                $user_id = $file[0]['id_usera'];
            }
            if($user != $user_id)
            {
                return abort(403, 'Unauthorized');
            }
            $pdf = array_column($file, 'pdf');
            if(empty($pdf))
            {
                return abort(404, 'File not found');
            }
            return Additional::output_file($pdf[0], 'pdf_'.$this->get['ticket'].'.pdf', 'pdf');
        }
        return abort(403, 'Unauthorized');
    }
    
    private function tabInsurances()
    {
        $this->tabProfile();
        $settings = array ( 
            'add_paid_products'    => true,
        );
        $products = array ( 'ubezpieczenia' );
        $up = new UserProductsData();
        $up->setOutputProducts( $products );
        $up->setFormatSettings( $settings );
        $up->getProducts();
        $this->tpl_data = $up->rOutput();
    }
    
    private function tabVouchers()
    {
        $this->tabProfile();
        $settings = array ( 
            'add_paid_products'     => true,
            'add_voucher_vproducts' => true,
        );
        $products = array ( 'vprodukty' );
        $up = new UserProductsData();
        $up->setOutputProducts( $products );
        $up->setFormatSettings( $settings );
        $up->getProducts();
        $this->tpl_data = $up->rOutput();
        $pack_vouchers = Userproducts::getPackVouchers();
        
        if ( $pack_vouchers )
        {
            $this->tpl_data['products']['zestawy'] = $pack_vouchers;
        }
    }
    
    private function tabCoupons()
    {
        $this->tabProfile();
        $settings = array ( 'add_paid_products' => true );
        $products = array ( 'kupony' );
        $up = new UserProductsData();
        $up->setOutputProducts( $products );
        $up->setFormatSettings( $settings );
        $up->getProducts();
        $this->tpl_data = $up->rOutput();
    }
    
    private function tabStamps()
    {
        $this->tabProfile();
        $products = array ( 'pieczatki' );
        $up = new UserProductsData();
        $up->setOutputProducts( $products );
        $up->getProducts();
        $up->formatOutput( 'pieczatki_account' );
        $this->tpl_data = $up->rOutput();
        
        $products = array ( 'kupony' );
        $settings = array ( 'add_used_stamps' => true );
        $up = new UserProductsData();
        $up->setOutputProducts( $products );
        $up->setFormatSettings( $settings );
        $up->getProducts();
        $used_stamps = $up->rOutput();
        
        if ( $used_stamps )
        {
            $this->tpl_data['products'] = array_merge( $this->tpl_data['products'], $used_stamps['products'] ); 
        }
    }

    private function tabBon()
    {
        $this->tabProfile();
        $this->tpl_data['bon_sum']     = Bon::getBonSum(true, false);   
        $this->tpl_data['bon_history'] = Bon::getBonHistory();
        // Tools::PA($this->tpl_data); die();
    }

    private function tabProduct()
    {
        $this->tabProfile();
        $heading_type = array (
            'karty'         => 'karty',
            'karnety'       => 'karnetu',
            'ubezpieczenia' => 'ubezpieczenia',
            'zamowienia'    => 'zamówienia',
            'vouchery'      => 'vouchera',
            'kupony'        => 'kuponu',
        );

        switch ( $this->get['p_type'] )
        {
            default:
                $settings = array ();
                break;

            case 'zamowienia':
                $this->tpl_data['address_labels'] = Additional::AdresFormLabels( true );
                $settings = array (
                    'set_resorts_string' => true,
                );
                break;

            case 'karty':
                $settings = array (
                    'add_card_tickets'     => true,
                    'set_resorts_string'   => true,
                    'add_ticket_days_left' => true,
                );
                break;

            case 'karnety':
                $settings = array (
                    'set_resorts_string'   => true,
                    'add_ticket_days_left' => true,
                    'add_ticket_insurance' => true,
                );
                break;
        }
        
        $settings['add_product_pdf'] = true;
        $up = new UserProductsData();
        $up->setProductId( $this->get['id'] );
        $up->setProductType( $this->get['p_type'] );
        $up->setFormatSettings( $settings );
        if ( !$up->getProduct() )
        {
            $this->get['tab'] = null;
            $this->main();
            return false;
        }
        else
        {
            $this->tpl_data['heading_type'] = $heading_type[$this->get['p_type']];
            $this->tpl_data['redirect']     = $this->get['redirect'];
            $this->tpl_data['product']      = $up->rOutput();
            $this->tpl_data['uid']      = User::GetUserID();
            $this->app->AddDecoratorData( 'p_tpl', $this->get['p_type'] . '_single' );
//            if($this->tpl_data['heading_type'] == 'vouchera') $this->tpl_data['heading_type'] = 'biletu';
        }
    }

    public function orderInvoice()
    {
        $this->tabProfile();
        $this->app->SetDecorator('DirectOut');
        $id      = $this->get['id'] ? $this->get['id'] : false;
        $linkdata = Crypt_SSL::decodeLink($id);
        if (empty($linkdata)) {
            $this->app->SetDecorator( 'DirectOut' );
            echo 'Error 3001.';
            die();
        }

        if (empty($linkdata[1]) || ($linkdata[1] != 'userinvoice' && $linkdata[1] != 'faktura')) {
            $this->app->SetDecorator( 'DirectOut' );
            echo 'Error 3002.';
            die();
        }

        $id = $linkdata[2];


        $data    = $this->post['data'] ? $this->post['data'] : false;
        if ($linkdata[1] == 'userinvoice') {
            $invoice = Faktury::getOrderInvoice($id, $data, false, '', true);
        } else {
            $invoice = Faktury::getNoLoginOrderInvoice($id);
        }

        if(!$invoice['result'])
        {
            if($invoice['error'])
            {
                $this->app->SetDecorator( 'DirectOut' );
                echo $invoice['error'];
            }
            elseif($invoice['no_data'])
            {
                $this->tpl_data['id'] = $id;
                $this->display('invoice-data');
            }
            else
            {
                $this->app->SetDecorator( 'DirectOut' );
                echo 'Error 3003.';
            }
        }
        elseif($invoice['link']) {
            if($invoice['isfakturownia']) {
                redirect($invoice['link']);
                exit();
            }
            $number = $invoice['invoice_number'] ?:'zamowienie';
            Additional::output_file($invoice['link'], 'faktura_'.$number.'.pdf');
        }
        else {
            $this->app->SetDecorator( 'DirectOut' );
            echo 'Error 3004.';
        }
    }

    public function ajaxValidateInvoice()
    {
        $data = $this->post['data'];
        $response['errors'] = false;
        $response['ok']     = false;

        if ( $data )
        {
            $val[] = array($data['nazwa'],  'REQUIRED', $this->app->Lang('Nazwa','F_Konto użytkownika'));
            $val[] = array($data['adres'],  'ADRES',    $this->app->Lang('Adres','F_Konto użytkownika'));
            $val[] = array($data['adres'],  'REQUIRED', $this->app->Lang('Adres','F_Konto użytkownika'));
            $val[] = array($data['kod'],    'KOD',      $this->app->Lang('Kod','F_Konto użytkownika'));
            $val[] = array($data['kod'],    'REQUIRED', $this->app->Lang('Kod','F_Konto użytkownika'));
            $val[] = array($data['miasto'], 'CITY',     $this->app->Lang('Miasto','F_Konto użytkownika'));
            $val[] = array($data['miasto'], 'REQUIRED', $this->app->Lang('Miasto','F_Konto użytkownika'));

            $val = Validation::ValForm( $val );
        
            if($val['errors'])
            {
                foreach($val['warnings'] as $warning)
                {
                    $response['errors'] = Tools::makeErrorsString($response['errors'], $warning);
                }
            }

            if(!$response['errors'])
            {
                $response['ok'] = true;
            }
        }
        else
        {
            $response['errors'] = Tools::makeErrorsString($response['errors'], $this->app->Lang('Błąd. Brak danych.','F_Konto użytkownika'));
        }

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function ajaxCancelOrder()
    {
        $response['ok'] = false;
        $id = $this->post['id'];

        if(floatval($id) > 0)
        {
            if(Zamowienie::cancelOrder($id))
            {
                $response['ok'] = true;
            }
        }

        $this->app->ADD('response', json_encode($response));
        $this->app->SetDecorator('Ajax');
    }

    public function ajaxChangeData()
    {
        $this->ajax_data = $this->post['data'];
        $this->changeData();
        $this->app->ADD( 'response', json_encode( $this->ajax_resp ) );
        $this->app->SetDecorator( 'Ajax' );
    }
    
    public function ajaxChangeCardDesc()
    {
        if ( $this->post['id_karty'] )
        {
            $result = Userproducts::changeCardDesc( $this->post['id_karty'], $this->post['opis_karty'] );
            
            if ( false === $result )
            {
                
                $response['error'] = true;
            }
            else
            {
                $response['ok'] = true;
            }
        }
        else
        {
            $response['error'] = true;
        }
        
        $this->app->ADD( 'response', json_encode( $response ) );
        $this->app->SetDecorator( 'Ajax' );
    }

    protected function changeData()
    {
        switch ( $this->ajax_data['action'] )
        {
            case 'invoice':
                foreach ( $post as &$p )
                {
                    $p = trim( $p );
                }

                $val[] = array($dane['imie'],                   'NAME',       $this->app->Lang('Imię','F_Konto użytkownika'));
                $val[] = array($dane['imie'],                   'REQUIRED',   $this->app->Lang('Imię','F_Konto użytkownika'));
                $val[] = array(array( $dane['imie'], 255 ),     'MAX_LENGTH', $this->app->Lang('Imię','F_Konto użytkownika'));
                $val[] = array($dane['nazwisko'],               'SURNAME',    $this->app->Lang('Nazwisko','F_Konto użytkownika'));
                $val[] = array($dane['nazwisko'],               'REQUIRED',   $this->app->Lang('Nazwisko','F_Konto użytkownika'));
                $val[] = array(array( $dane['nazwisko'], 255 ), 'MAX_LENGTH', $this->app->Lang('Nazwisko','F_Konto użytkownika'));
                $val[] = array($dane['email'],                  'EMAIL',      $this->app->Lang('Email','F_Konto użytkownika'));
                $val[] = array($dane['email'],                  'REQUIRED',   $this->app->Lang('Email','F_Konto użytkownika'));
                $val[] = array(array( $dane['email'], 255 ),    'MAX_LENGTH', $this->app->Lang('Email','F_Konto użytkownika'));
                $val[] = array($dane['adres'],                  'ADRES',      $this->app->Lang('Adres','F_Konto użytkownika'));
                $val[] = array($dane['adres'],                  'REQUIRED',   $this->app->Lang('Adres','F_Konto użytkownika'));
                $val[] = array(array( $dane['adres'], 255 ),    'MAX_LENGTH', $this->app->Lang('Adres','F_Konto użytkownika'));
                $val[] = array($dane['kod'],                    'KOD',        $this->app->Lang('Kod','F_Konto użytkownika'));
                $val[] = array($dane['kod'],                    'REQUIRED',   $this->app->Lang('Kod','F_Konto użytkownika'));
                $val[] = array(array( $dane['kod'], 6 ),        'MAX_LENGTH', $this->app->Lang('Kod','F_Konto użytkownika'));
                $val[] = array($dane['miasto'],                 'CITY',       $this->app->Lang('Miasto','F_Konto użytkownika'));
                $val[] = array($dane['miasto'],                 'REQUIRED',   $this->app->Lang('Miasto','F_Konto użytkownika'));
                $val[] = array(array( $dane['miasto'], 255 ),   'MAX_LENGTH', $this->app->Lang('Miasto','F_Konto użytkownika'));
                $val[] = array($dane['tel'],                    'NUMERIC',    $this->app->Lang('Telefon','F_Konto użytkownika'));
                $val[] = array($dane['tel'],                    'REQUIRED',   $this->app->Lang('Telefon','F_Konto użytkownika'));

                if ( $post['firma'] )
                {
                    $val[] = array ( $post['nip'], 'REQUIRED', 'Nip' );
                }

                $val = Validation::ValForm( $val );

                if ( $val['errors'] )
                {
                    $this->alerts['danger'] = $val['warnings'];
                }
                elseif ( User::ChangeUserMetaData( $post ) )
                {
                    $this->alerts['success'][] = $this->app->Lang('Dane zostały zaktualizowane','F_Konto użytkownika');
                }
                else
                {
                    $this->alerts['danger'][] = $this->app->Lang('Nie udało się zaktualizować danych','F_Konto użytkownika');
                }

                break;

            case 'userdata':
                foreach ( $this->ajax_data['userdata'] as &$p )
                {
                    $p = trim( $p );
                }

                if ( $this->ajax_data['userdata']['imie'] )
                {
                    $val[] = array($this->ajax_data['userdata']['imie'],             'NAME',       $this->app->Lang('Imię','F_Konto użytkownika'));
                    $val[] = array(array($this->ajax_data['userdata']['imie'], 64 ), 'MAX_LENGTH', $this->app->Lang('Imię','F_Konto użytkownika'));
                }

                if ( $this->ajax_data['userdata']['nazwisko'] )
                {
                    $val[] = array($this->ajax_data['userdata']['nazwisko'],              'SURNAME',    $this->app->Lang('Nazwisko','F_Konto użytkownika'));
                    $val[] = array(array( $this->ajax_data['userdata']['nazwisko'], 64 ), 'MAX_LENGTH', $this->app->Lang('Nazwisko','F_Konto użytkownika'));
                }

                if ( $this->ajax_data['userdata']['email'] != User::GetUserEmail() )
                {
                    $val[] = array($this->ajax_data['userdata']['email'],               'EMAIL',      $this->app->Lang('Email','F_Konto użytkownika'));
                    $val[] = array(array( $this->ajax_data['userdata']['email'], 200 ), 'MAX_LENGTH', $this->app->Lang('Email','F_Konto użytkownika'));
                    $val[] = array($this->ajax_data['userdata']['email'],               'EMAILUSED' );
                }

                $val = Validation::ValForm( $val );

                if ( $val['errors'] )
                {
                    $this->makeError( $val['warnings'] );
                }
                elseif ( User::ChangeUserData( $this->ajax_data['userdata'] ) )
                {
                    $this->ajax_resp['ok'] = true;
                }
                else
                {
                    $this->ajax_resp['error'] = $this->app->Lang('Podczas aktualizacji danych wystąpił błąd','F_Konto użytkownika');
                }

                return true;

            case 'password':
                $usr = &$this->ajax_data['userdata'];
                $val[] = array(array($usr['nowe_haslo'], 100 ), 'MAX_LENGTH', $this->app->Lang('Nowe hasło','F_Konto użytkownika'));
                $val[] = array(array($usr['nowe_haslo'], 6 ),   'MIN_LENGTH', $this->app->Lang('Nowe hasło','F_Konto użytkownika'));
                $val[] = array($usr['haslo'], 'CONFPASS' );
                $val[] = array(array($usr['nowe_haslo'], $usr['powt_nowe_haslo']), 'MATCHES', array($this->app->Lang('Nowe hasło','F_Konto użytkownika'), $this->app->Lang('Powtórz nowe hasło','F_Konto użytkownika')));

                $val = Validation::ValForm( $val );

                if ( $val['errors'] )
                {
                    $this->makeError( $val['warnings'] );
                }
                elseif ( User::ChangeUserData( $this->ajax_data['userdata'] ) )
                {
                    $this->ajax_resp['ok'] = true;
                }
                else
                {
                    $this->ajax_resp['error'] = $this->app->Lang('Podczas aktualizacji danych wystąpił błąd','F_Konto użytkownika');
                }

                break;
        }
    }

    public function ajaxChangeAddress()
    {
        $this->ajax_data = $this->post['data'];
        $this->changeAddress();
        $this->app->ADD( 'response', json_encode( $this->ajax_resp ) );
        $this->app->SetDecorator( 'Ajax' );
    }

    private function changeAddress()
    {
        switch ( $this->ajax_data['action'] )
        {
            default:
                foreach ( $this->ajax_data as &$p )
                {
                    $p = trim( $p );
                }

                $val[] = array ( $this->ajax_data['imie'],     'NAME',    $this->app->Lang('Imię','F_Konto użytkownika'));
                $val[] = array ( $this->ajax_data['nazwisko'], 'NAME',    $this->app->Lang('Nazwisko','F_Konto użytkownika'));
                $val[] = array ( $this->ajax_data['email'],    'EMAIL',   $this->app->Lang('Email','F_Konto użytkownika'));
                $val[] = array ( $this->ajax_data['adres'],    'ADRES',   $this->app->Lang('Adres','F_Konto użytkownika'));
                $val[] = array ( $this->ajax_data['kod'],      'KOD',     $this->app->Lang('Kod','F_Konto użytkownika'));
                $val[] = array ( $this->ajax_data['miasto'],   'CITY',    $this->app->Lang('Miejscowość','F_Konto użytkownika'));
                $val[] = array ( $this->ajax_data['tel'],      'NUMERIC', $this->app->Lang('Telefon','F_Konto użytkownika'));

                if ( $this->ajax_data['firma'] )
                {
                    $val[] = array ( $this->ajax_data['nip'], 'REQUIRED', 'Nip' );
                }

                $val = Validation::ValForm( $val );

                if ( $val['errors'] )
                {
                    $this->makeError( $val['warnings'] );
                }
                else
                {
                    $this->ajax_resp['address'] = User::ChangeUserMetaData( $this->ajax_data );
                }

                return true;

            case 'delete':
                if ( $this->sdb->delete( 'uzytkownik_meta', array ( 'id' => $this->ajax_data['id'] ) ) )
                {
                    $this->ajax_resp['address'] = $this->ajax_data;
                }
                else
                {
                    $this->ajax_resp['error'] = $this->app->Lang('Podczas usuwania adresu wystąpił błąd','F_Konto użytkownika');
                }

                return true;
        }
    }

    protected function workOffer()
    {
        if ( $this->get['id_ticket'] && $this->get['hash'] && $this->get['parentid'] )
        {
            $this->mdb = Mymongo::activate();
            $sprawa = $this->mdb->case_list( 1, false, $this->get['id_ticket'], true );

            if ( $sprawa )
            {
                if ( $sprawa['hash'] == $this->get['hash'] && $sprawa['oferta_robocza'] == $this->get['parentid'] )
                {
                    $offer = $this->mdb->mget_offer_details( $this->get['parentid'] );

                    include_once(ENSETTINGSDIR . 'Decorator.Smarty.php');
                    include_once('application/admin_v4_0/modules/offers/AdminOffers.php');

                    if ( $offer['karty'] )
                    {
                        $karty = $offer['karty'];
                        foreach ( $karty as $k => $v )
                        {
                            $l = Tools::GetPartnerProductLangs( $v['typ_karty'], 'hash' );
                            $v['typ_karty'] = $l['nazwa'][LANG];
                            $this->app->ADD( 'pr_kart', Tools::getPartners( 8 ) );
                            $this->app->ADD( 'typy_kart', Tools::GetPartnerAllProducts( $karta['producent_kart'] ) );
                            $this->app->ADD( 'producent_kart', $karta['producent_kart'] );
                            $this->app->ADD( 'postback', $v );
                            $this->app->ADD( 'cardid', $k + 1 );
                            $this->app->ADD( 'pzawieszone', $pzawieszone );
                            $this->app->SetTemplate( '../../partner/templates/modules/partner/offers/view_karty.tpl' );
                            $doc = new DecoratorSmarty( $this->app );
                            $kartys[] = $doc->output;
                            $this->app->ResetDecoratorData();
                        }
                    }

                    if ( $offer['karnety'] )
                    {
                        $karnety = $offer['karnety'];
                        foreach ( $karnety as $k => $karnet )
                        {
                            $karnet['obowiazuje']['time_starth'] = substr( $karnet['obowiazuje']['time_start'], 0, 2 );
                            $karnet['obowiazuje']['time_startm'] = substr( $karnet['obowiazuje']['time_start'], 2, 2 );
                            $karnet['obowiazuje']['time_stoph'] = substr( $karnet['obowiazuje']['time_stop'], 0, 2 );
                            $karnet['obowiazuje']['time_stopm'] = substr( $karnet['obowiazuje']['time_stop'], 2, 2 );
                            $this->app->ADD( 'ticketid', $k + 1 );
                            $this->app->ADD( 'pzawieszone', $pzawieszone );
                            if ( $karnet['prowizja'][0]['grupa'] )
                            {
                                $this->app->ADD( 'grupaobrotowa', $this->sdb->get_field_val( 'prowizja_grupy', 'nazwa', array ( array ( 'id', $karnet['prowizja'][0]['grupa'], 'INT' ) ) ) );
                            }
                            else
                                $this->app->ADD( 'grupaobrotowa', 'brak' );
                            $sql_r = 'SELECT id, nazwa FROM ' . TABLE_PREFIX . 'prowizja_grupy WHERE id_oferty="' . $id . '"';
                            $this->app->ADD( 'grupyobrotowe', $this->sdb->select( $sql_r ) );
                            $this->app->ADD( 'listakart', Tools::GetPartnerTypeProducts( 2 ) );
                            $this->app->ADD( 'postback', $karnet );
                            $this->app->SetTemplate( '../../partner/templates/modules/partner/offers/view_karnety.tpl' );
                            $doc = new DecoratorSmarty( $this->app );
                            $tickets[] = $doc->output;
                            $this->app->ResetDecoratorData();
                        }
                    }

                    if ( $offer['ubezpieczenia'] )
                    {
                        $ubezpieczenia = $offer['ubezpieczenia'];
                        foreach ( $ubezpieczenia as $l => $ubezp )
                        {
                            $ubezpieczyciele = Tools::GetPartners( 3, true );
                            $sprodukty = Tools::GetPartnerTypeProducts( 3, true );
                            $partnersmeta = Tools::GetPartnersMetadata( 3 );
                            foreach ( $ubezpieczyciele as $k => $v )
                            {
                                foreach ( $sprodukty as $kk => $vv )
                                {
                                    if ( $vv['parent_id'] == $k )
                                    {
                                        $pr['ubezpieczyciel'] = $v['shortname'] . ' - ' . $vv['name'];
                                        $pr['partner_id'] = $v['id'];
                                        $pr['produkt_hash'] = $vv['hash'];
                                        $pr['czas_trwania'] = $vv['metadata']['czas_trwania'];
                                        $pr['cenam_brutto'] = $vv['metadata']['cenam_brutto'];
                                        $pr['cena_brutto'] = $vv['metadata']['cena_brutto'];
                                        $pr['koszt_brutto'] = $vv['metadata']['koszt_brutto'];
                                        $pr['koszt_vat'] = $vv['metadata']['koszt_vat'];
                                        $pr['cena_vat'] = $vv['metadata']['cena_vat'];
                                        $pr['czas_trwania'] = $vv['metadata']['czas_trwania'];
                                        $pr['uid'] = $vv['id'];
                                        $pr['nrumowy'] = $partnersmeta[$k]['nrumowy'];
                                        $prod[] = $pr;
                                    }
                                }
                            }
                            $this->app->ADD( 'postback', $ubezp );
                            $this->app->ADD( 'produkty', $prod );
                            $this->app->ADD( 'insuranceid', $l + 1 );
                            $this->app->ADD( 'pzawieszone', $pzawieszone );
                            $this->app->SetTemplate( '../../partner/templates/modules/partner/offers/view_ubezpieczenia.tpl' );
                            $doc = new DecoratorSmarty( $this->app );
                            $insurances[] = $doc->output;
                            $this->app->ResetDecoratorData();
                            $prod = array ();
                        }
                    }

                    if ( $offer['vouchery'] )
                    {
                        $vouchery = $offer['vouchery'];
                        if ( !is_array( $vouchery ) )
                            $vouchery = array ();
                        foreach ( $vouchery as $k => $v )
                        {
                            $this->app->ADD( 'listakart', Tools::GetPartnerTypeProducts( 2 ) );
                            $this->app->ADD( 'voucherid', $k + 1 );
                            $this->app->ADD( 'pzawieszone', $pzawieszone );
                            if ( $v['prowizja'][0]['grupa'] )
                            {
                                $this->app->ADD( 'grupaobrotowa', $this->sdb->get_field_val( 'prowizja_grupy', 'nazwa', array ( array ( 'id', $v['prowizja'][0]['grupa'], 'INT' ) ) ) );
                            }
                            else
                                $this->app->ADD( 'grupaobrotowa', 'brak' );
                            $this->app->ADD( 'postback', $v );
                            $this->app->SetTemplate( "../../partner/templates/modules/partner/offers/view_vouchery.tpl" );
                            $this->app->ResetWidgetsData();
                            $doc = new DecoratorSmarty( $this->app );
                            $vouchers[] = $doc->output;
                            $this->app->ResetDecoratorData();
                        }
                    }

                    if ( $offer['produkty_wirtualne'] )
                    {
                        $vprodukty = $offer['produkty_wirtualne'];
                        if ( !is_array( $vprodukty ) )
                            $vprodukty = array ();
                        foreach ( $vprodukty as $k => $v )
                        {
                            $this->app->ADD( 'postback', $v );
                            $this->app->ADD( 'cardid', $k + 1 );
                            $this->app->ADD( 'pzawieszone', $pzawieszone );
                            $this->app->SetTemplate( "../../partner/templates/modules/partner/offers/view_virtualp.tpl" );
                            $this->app->ResetWidgetsData();
                            $doc = new DecoratorSmarty( $this->app );
                            $vproducts[] = $doc->output;
                            $this->app->ResetDecoratorData();
                        }
                    }
                    $this->app->ADD( 'cards', $kartys );
                    $this->app->ADD( 'tickets', $tickets );
                    $this->app->ADD( 'insurances', $insurances );
                    $this->app->ADD( 'vouchers', $vouchers );
                    $this->app->ADD( 'vprodukty', $vproducts );
                    $this->app->ADD( 'viewmode', 'view' );
                    if ( $offer['status']['id'] == '2' )
                    {
                        $offer['sigcode'] = Auth::MakeAuthHash( $offer );
                    }

                    if ( isset( $offer['przewoznicy'] ) )
                    {
                        $offer['przewoznicy'] = $this->ReindexArray( $offer['przewoznicy'], 'hash' );
                    }
                    if ( isset( $offer['partnerzy_sp'] ) )
                    {
                        $offer['partnerzy_sp'] = $this->ReindexArray( $offer['partnerzy_sp'] );
                    }
                    if ( isset( $offer['systemy_platnosci'] ) )
                    {
                        $offer['systemy_platnosci'] = $this->ReindexArray( $offer['systemy_platnosci'] );
                    }
                    $offer['offerid'] = $offer['_id']['$id'];
                    $this->app->ADD( 'osrodki', $this->_prepareSkiresorts() );
                    $this->app->ADD( 'kurierzy', $this->_prepareDeliverers() );
                    $this->app->ADD( 'systempl', $this->_preparePaymantSystems() );
                    $this->app->ADD( 'brokers', $this->_prepareBrokers() );
                    $this->app->ADD( 'brokersfr', $this->_prepareFrBrokers() );
                    $this->app->ADD( "postback", $offer );
                    $this->app->ADD( 'section', 'main' );
                    $this->app->ADD( 'viewmode', 'view' );
                }
                else
                {
                    $this->alerts['danger'][] = $this->app->Lang('Nieprawidłowy kod sprawy lub oferty','F_Konto użytkownika');
                }
            }
            else
            {
                $this->alerts['danger'][] = $this->app->Lang('Sprawa nie istnieje lub nie jest już aktywna','F_Konto użytkownika');
            }
        }
        $this->display( 'mediation-workoffer' );
    }
    
    private function ReindexArray(array $array, $fname = 'id'){
        foreach($array as $v){
        $rarray[$v[$fname]] = $v;
        }
        return $rarray;
    }
    
    private function _prepareSkiresorts() {
        $this->_preparePartners();
        $os = $this->partners['osrodkin'];
        return $os;
    }
    
    private function _prepareDeliverers() {
        $this->_preparePartners();
        $kurierzy = $this->partners['kurierzy'];
        $products_deliv = Tools::GetPartnerTypeProducts(5, true);
        foreach ($kurierzy as $k => $v) {
            foreach ($products_deliv as $kk => $vv) {
                if ($vv['parent_id'] == $k) {
                    $out['nazwa'] = $v['shortname'] . ' - ' . $vv['name'];
                    $out['netto'] = $vv['metadata']['cena_z_prowizja_netto'];
                    $out['vat'] = $vv['metadata']['vat'];
                    $out['brutto'] = $vv['metadata']['cena_z_prowizja_brutto'];
                    $out['ids'] = $k . 'p' . $vv['id'];
                    $out['kid'] = $k;
                    $out['kname'] = $v['shortname'];
                    $out['prid'] = $vv['hash'];
                    $out['prname'] = $vv['name'];
                    $prod[] = $out;
                }
            }
        }
        return $prod;
    }
    
    private function _prepareBrokers() {
        $this->_preparePartners();
        $brokers = $this->partners['brokers'];
        $brokersids = array_keys($brokers);
        $sql = 'SELECT id_parent, value, properties FROM ' . TABLE_PREFIX . 'partners_meta WHERE current=1 AND id_parent IN (' . join(',', $brokersids) . ')';
        $prow = $this->sdb->select($sql);
        foreach ($prow as $v) {
            $brokers[$v['id_parent']][$v['properties']] = number_format((float) $v['value'], 2);
        }
        return $brokers;
    }

    /**
     * Przygotowanie tablicy z danymi sprzedawców franczyzowych do wyświetlenia na stronie tworzenia oferty
     * @return type
     */
    private function _prepareFrBrokers() {
        $this->_preparePartners();
        $brokers = $this->partners['brokersfr'];
        $brokersids = array_keys($brokers);
        $sql = 'SELECT id_parent, value, properties FROM ' . TABLE_PREFIX . 'partners_meta WHERE current=1 AND id_parent IN (' . join(',', $brokersids) . ')';
        $prow = $this->sdb->select($sql);
        foreach ($prow as $v) {
            $brokers[$v['id_parent']][$v['properties']] = number_format((float) $v['value'], 2);
        }
        return $brokers;
    }
    
    private function _preparePartners() {
        if (!is_null($this->partners))
            return;
        $partnerzy = Tools::GetPartners();
        if (!is_array($partnerzy))
            $partnerzy = array();
        foreach ($partnerzy as $k => $partner) {
            switch ($partner['partnertype']) {
                case 1:
                    $brokers[$partner['id']] = $partnerzy[$k];
                    break;
                case 2:
                    $osrodkin[$partner['id']] = $partnerzy[$k];
                    break;
                case 3:
                    $ubezpieczyciele[$partner['id']] = $partnerzy[$k];
                    break;
                case 4:
                    $systemypl[$partner['id']] = $partnerzy[$k];
                    break;
                case 5:
                    $kurierzy[$partner['id']] = $partnerzy[$k];
                    break;
                case 6:
                    $brokersfr[$partner['id']] = $partnerzy[$k];
                    break;
                default:
                    break;
            }
            $partners['brokers'] = $brokers;
            $partners['brokersfr'] = $brokersfr;
            $partners['osrodkin'] = $osrodkin;
            $partners['ubezpieczyciele'] = $ubezpieczyciele;
            $partners['systemypl'] = $systemypl;
            $partners['kurierzy'] = $kurierzy;
            $this->partners = $partners;
        }
    }
    
    private function _preparePaymantSystems() {
        $this->_preparePartners();
        $systemypl = $this->partners['systemypl'];
        foreach ($systemypl as $k => $v) {
            $meta = Tools::GetPartnerMetadata($k);
            foreach ($meta as $kk => $vv) {
                $systemypl[$k][$kk] = $vv;
            }
        }
        return $systemypl;
    }


    public function AuthAjax()
    {
        $post = $this->app->postparam;
        $response = Auth::Authorize( $post['pin'], $post['data_post'] );
        $this->app->ADD( 'response', json_encode( $response ) );
        $this->app->SetDecorator( 'Ajax' );
    }

    private function makeError( $error_array )
    {
        $this->ajax_resp['error'] = '';

        foreach ( $error_array as $error )
        {
            $this->ajax_resp['error'].= $error . '<br>';
        }

        $this->ajax_resp['error'] = rtrim( $this->ajax_resp['error'], '<br>' );
        return true;
    }


    protected function display($tpl, $title = false)
    {
        if($title)
        {
            $this->widget_header_data['title'] = $this->app->Lang($title, 'F_Konto użytkownika');            
        }

        if(!$this->widget_header_data['title'])
        {
            $this->widget_header_data['title'] = $this->app->Lang('Moje konto', 'F_Konto użytkownika');
        }

        $this->widget_accnav_data['tab'] = $this->tab;
//        die(var_dump($this->widget_accnav_data['profile']['p_quantity']));
        $this->app->AddWidgetsData( 'topnav', $this->meta['header'] );
        $this->app->AddWidgetsData( 'footer', $this->meta['footer'] );
        $this->app->AddWidgetsData( 'header', $this->widget_header_data );
        $this->app->AddWidgetsData( 'breadcrumbs' );
        $this->app->AddWidgetsData( 'accountnav', $this->widget_accnav_data );
        $this->app->AddWidgetsData( 'alerts', $this->alerts );
        $this->app->AddDecoratorData( 'data', $this->tpl_data );
        $this->app->AddDecoratorData( 'tpl', $this->tpl_conf['tpl'] );
        $this->app->SetTemplate( 'modules/account/' . $tpl . '.tpl' );
    }
}
