<?php

/**
 * Obsługa breadcrumbs na stronie
 * @package Application
 * @subpackage Widgets
 */
class Breadcrumbs {

    private $baza = null;
    private $app = null;
    private $config = array(
        'pagetitle' => SITE_NAME
    );
    public $output = null;

    public function __construct($app, $params = array()) {
        if (!is_array($params))
            $params = array();
        $this->baza = Database::activate();
        $this->app = &$app;
        $params = $this->mkconfig($params);
        $this->DisplayBreadcrumbs($params);
    }

    private function mkconfig($params) {
        $this->config['breadcrumbs'] = array("url" => "", "label" => $this->app->Lang("Home",'F_Strona główna'), "title" => $this->app->Lang("Home",'F_Strona główna'));
        return array_merge($this->config, $params);
    }

    private function DisplayBreadcrumbs($params) {
        $return = array(
            'template' => "widgets/breadcrumbs.tpl",
            'data' => $params
        );
        $this->output = $return;
    }

}
