<?php

/**
 * Obsługa menu konta partnera
 * @package Application
 * @subpackage Widgets
 */
class Partnernav {

    private $config = array();
    private $baza   = null;
    private $app    = null;
    public  $output = null;

    public function __construct( $app, $params = array() ) 
    {
        if ( ! is_array( $params ) ) {
            $params = array();
        }
        $this->baza = Database::activate();
        $this->app  = &$app;
        $params = $this->mkconfig( $params );
        $this->DisplayAccountnav( $params );
    }

    private function mkconfig( $params ) {
        return array_merge( $this->config, $params );
    }

    private function DisplayAccountnav( $params ) {
        $return = array(
            'template' => "widgets/accountnav.tpl",
            'data' => $params
        );
        $this->output = $return;
    }

}
